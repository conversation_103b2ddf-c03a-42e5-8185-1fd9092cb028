'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { showErrorToast, showSuccessToast } from '@/components/ui/soft-toasts';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import type { Promotion } from '@/lib/types';
import { DollarSign, Edit, Percent, Plus, Search, Tag, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function AdminPromotionsPage() {
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPromotion, setEditingPromotion] = useState<Promotion | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    type: 'percentage' as 'percentage' | 'fixed',
    value: '',
    minAmount: '',
    maxDiscount: '',
    validFrom: '',
    validTo: '',
    isActive: true,
  });

  useEffect(() => {
    const fetchPromotions = async () => {
      try {
        const res = await fetch('/api/promotions', { cache: 'no-store' });
        const data = await res.json().catch(() => ({}));
        if (res.ok) {
          const rawItems = (data && (data.data?.items || data.items || data.data || data)) || [];
          const itemsArray = Array.isArray(rawItems) ? rawItems : [];
          const mapped: Promotion[] = itemsArray.map((it: any) => ({
            id: String(it.id ?? it.promotion_id ?? ''),
            code: it.code ?? it.promo_code ?? '',
            name: it.name ?? it.title ?? '',
            description: it.description ?? '',
            type: (it.type === 'percentage' ? 'percentage' : 'fixed') as Promotion['type'],
            value: Number(it.value ?? it.amount ?? 0),
            minAmount:
              it.min_amount !== undefined && it.min_amount !== null
                ? Number(it.min_amount)
                : undefined,
            maxDiscount:
              it.max_discount !== undefined && it.max_discount !== null
                ? Number(it.max_discount)
                : undefined,
            validFrom: it.valid_from ?? it.start_date ?? it.validFrom ?? '',
            validTo: it.valid_to ?? it.end_date ?? it.validTo ?? '',
            isActive: Boolean(it.is_active ?? it.isActive ?? false),
          }));
          setPromotions(mapped);
        } else {
          showErrorToast(
            (data && (data.message || data.error)) || 'Không tải được danh sách khuyến mãi'
          );
        }
      } catch (e) {
        showErrorToast('Không thể kết nối tới máy chủ.');
      }
    };
    fetchPromotions();
  }, []);

  const filteredPromotions = promotions.filter(
    (promo) =>
      promo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      promo.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const activePromotions = promotions.filter((p) => p.isActive).length;
  const totalPromotions = promotions.length;

  const handleCreatePromotion = async () => {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      if (!token) {
        showErrorToast('Bạn chưa đăng nhập.');
        return;
      }

      // Validate required fields
      if (!formData.code.trim() || !formData.name.trim()) {
        showErrorToast('Vui lòng nhập Mã và Tên khuyến mãi.');
        return;
      }
      const valueNum = Number(formData.value || 0);
      if (!valueNum || valueNum <= 0) {
        showErrorToast('Giá trị giảm phải lớn hơn 0.');
        return;
      }
      if (formData.type === 'percentage' && (valueNum < 1 || valueNum > 100)) {
        showErrorToast('Giá trị phần trăm phải trong khoảng 1-100.');
        return;
      }

      // Build payload in backend-expected shape
      const payload = {
        code: formData.code.trim().toUpperCase(),
        name: formData.name,
        description: formData.description || null,
        type: formData.type,
        value: valueNum,
        min_amount: formData.minAmount ? Number(formData.minAmount) : 0,
        max_discount:
          formData.type === 'percentage' && formData.maxDiscount
            ? Number(formData.maxDiscount)
            : null,
        valid_from: formData.validFrom ? new Date(formData.validFrom).toISOString() : null,
        valid_to: formData.validTo ? new Date(formData.validTo).toISOString() : null,
        is_active: Boolean(formData.isActive),
      };

      const res = await fetch('/api/promotions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      const data = await res.json().catch(() => ({}));
      if (res.ok && data && (data.data || data.promotion)) {
        const it = data.data || data.promotion;
        const created: Promotion = {
          id: String(it.id ?? ''),
          code: it.code ?? '',
          name: it.name ?? '',
          description: it.description ?? '',
          type: (it.type === 'percentage' ? 'percentage' : 'fixed') as Promotion['type'],
          value: Number(it.value ?? 0),
          minAmount: it.min_amount != null ? Number(it.min_amount) : undefined,
          maxDiscount: it.max_discount != null ? Number(it.max_discount) : undefined,
          validFrom: it.valid_from ?? '',
          validTo: it.valid_to ?? '',
          isActive: Boolean(it.is_active ?? false),
        };
        setPromotions([...promotions, created]);
        setIsCreateDialogOpen(false);
        resetForm();
        showSuccessToast('Tạo khuyến mãi thành công');
      } else {
        showErrorToast((data && (data.message || data.error)) || 'Tạo khuyến mãi thất bại');
      }
    } catch (e) {
      showErrorToast('Không thể kết nối tới máy chủ.');
    }
  };

  const handleEditPromotion = () => {
    if (!editingPromotion) return;

    const updatedPromotions = promotions.map((p) => {
      if (p.id !== editingPromotion.id) return p;
      return {
        ...p,
        code: formData.code,
        name: formData.name,
        type: formData.type,
        value: Number(formData.value || 0),
        minAmount: formData.minAmount ? Number(formData.minAmount) : undefined,
        maxDiscount: formData.maxDiscount ? Number(formData.maxDiscount) : undefined,
        validFrom: formData.validFrom,
        validTo: formData.validTo,
        isActive: formData.isActive,
      } as Promotion;
    });
    setPromotions(updatedPromotions);
    setEditingPromotion(null);
    resetForm();
  };

  const handleDeletePromotion = (id: string) => {
    setPromotions(promotions.filter((p) => p.id !== id));
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      type: 'percentage',
      value: '',
      minAmount: '',
      maxDiscount: '',
      validFrom: '',
      validTo: '',
      isActive: true,
    });
  };

  const openEditDialog = (promotion: Promotion) => {
    setEditingPromotion(promotion);
    setFormData({
      code: promotion.code,
      name: promotion.name,
      description: promotion.description || '',
      type: promotion.type,
      value: promotion.value ? String(promotion.value) : '',
      minAmount: promotion.minAmount ? String(promotion.minAmount) : '',
      maxDiscount: promotion.maxDiscount ? String(promotion.maxDiscount) : '',
      validFrom: promotion.validFrom,
      validTo: promotion.validTo,
      isActive: promotion.isActive,
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quản lý khuyến mãi</h1>
          <p className="text-gray-600">Tạo và quản lý các chương trình khuyến mãi</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Tạo khuyến mãi
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Tạo khuyến mãi mới</DialogTitle>
              <DialogDescription>Tạo chương trình khuyến mãi mới cho khách hàng</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="code" className="pb-1">
                  Mã khuyến mãi
                </Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  placeholder="VD: WELCOME20"
                />
              </div>
              <div>
                <Label htmlFor="name" className="pb-1">
                  Tên khuyến mãi
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="VD: Chào mừng khách hàng mới"
                />
              </div>
              <div>
                <Label htmlFor="description" className="pb-1">
                  Mô tả
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="VD: Mô tả ngắn về chương trình khuyến mãi"
                />
              </div>
              <div>
                <Label htmlFor="type" className="pb-1">
                  Loại khuyến mãi
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: 'percentage' | 'fixed') =>
                    setFormData({ ...formData, type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Giảm theo phần trăm</SelectItem>
                    <SelectItem value="fixed">Giảm số tiền cố định</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="value" className="pb-1">
                  Giá trị giảm
                </Label>
                <Input
                  id="value"
                  type="number"
                  value={formData.value}
                  onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                  placeholder={formData.type === 'percentage' ? 'VD: 10%' : 'VD: 100.000đ'}
                />
              </div>
              <div>
                <Label htmlFor="minAmount" className="pb-1">
                  Giá trị đơn hàng tối thiểu
                </Label>
                <Input
                  id="minAmount"
                  type="number"
                  value={formData.minAmount}
                  onChange={(e) => setFormData({ ...formData, minAmount: e.target.value })}
                  placeholder="VD: 100.000đ"
                />
              </div>
              {formData.type === 'percentage' && (
                <div>
                  <Label htmlFor="maxDiscount" className="pb-1">
                    Giảm tối đa
                  </Label>
                  <Input
                    id="maxDiscount"
                    type="number"
                    value={formData.maxDiscount}
                    onChange={(e) => setFormData({ ...formData, maxDiscount: e.target.value })}
                    placeholder="VD: 50.000đ"
                  />
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="validFrom" className="pb-1">
                    Ngày bắt đầu
                  </Label>
                  <Input
                    id="validFrom"
                    type="date"
                    value={formData.validFrom}
                    onChange={(e) => setFormData({ ...formData, validFrom: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="validTo" className="pb-1">
                    Ngày kết thúc
                  </Label>
                  <Input
                    id="validTo"
                    type="date"
                    value={formData.validTo}
                    onChange={(e) => setFormData({ ...formData, validTo: e.target.value })}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <Label htmlFor="isActive" className="pb-1">
                  Kích hoạt khuyến mãi
                </Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={handleCreatePromotion}>Tạo khuyến mãi</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng khuyến mãi</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPromotions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đang hoạt động</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activePromotions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tạm dừng</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {totalPromotions - activePromotions}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách khuyến mãi</CardTitle>
          <CardDescription>Quản lý tất cả các chương trình khuyến mãi</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm theo tên hoặc mã khuyến mãi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã</TableHead>
                <TableHead>Tên khuyến mãi</TableHead>
                <TableHead>Loại</TableHead>
                <TableHead>Giá trị</TableHead>
                <TableHead>Thời gian</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPromotions.map((promotion) => (
                <TableRow key={promotion.id}>
                  <TableCell className="font-medium">{promotion.code}</TableCell>
                  <TableCell>{promotion.name}</TableCell>
                  <TableCell>
                    <Badge variant={promotion.type === 'percentage' ? 'default' : 'secondary'}>
                      {promotion.type === 'percentage' ? 'Phần trăm' : 'Cố định'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {promotion.type === 'percentage'
                      ? `${promotion.value}%`
                      : formatCurrency(promotion.value)}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{formatDate(promotion.validFrom)}</div>
                      <div className="text-muted-foreground">
                        đến {formatDate(promotion.validTo)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={promotion.isActive ? 'default' : 'secondary'}>
                      {promotion.isActive ? 'Hoạt động' : 'Tạm dừng'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Dialog
                        open={editingPromotion?.id === promotion.id}
                        onOpenChange={(open) => {
                          if (!open) setEditingPromotion(null);
                        }}
                      >
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(promotion)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <DialogHeader>
                            <DialogTitle>Chỉnh sửa khuyến mãi</DialogTitle>
                            <DialogDescription>Cập nhật thông tin khuyến mãi</DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="edit-code" className="pb-1">
                                Mã khuyến mãi
                              </Label>
                              <Input
                                id="edit-code"
                                value={formData.code}
                                onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                                disabled
                              />
                            </div>
                            <div>
                              <Label htmlFor="edit-name" className="pb-1">
                                Tên khuyến mãi
                              </Label>
                              <Input
                                id="edit-name"
                                value={formData.name}
                                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                placeholder="VD: Summer Sale 18%"
                              />
                            </div>
                            <div>
                              <Label htmlFor="edit-description" className="pb-1">
                                Mô tả
                              </Label>
                              <Textarea
                                id="edit-description"
                                value={formData.description}
                                onChange={(e) =>
                                  setFormData({ ...formData, description: e.target.value })
                                }
                                placeholder="VD: Mô tả ngắn về chương trình khuyến mãi"
                              />
                            </div>
                            <div>
                              <Label htmlFor="edit-type" className="pb-1">
                                Loại khuyến mãi
                              </Label>
                              <Select
                                value={formData.type}
                                onValueChange={(value: 'percentage' | 'fixed') =>
                                  setFormData({ ...formData, type: value })
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="percentage">Giảm theo phần trăm</SelectItem>
                                  <SelectItem value="fixed">Giảm số tiền cố định</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label htmlFor="edit-value" className="pb-1">
                                Giá trị giảm
                              </Label>
                              <Input
                                id="edit-value"
                                type="number"
                                value={formData.value}
                                onChange={(e) =>
                                  setFormData({ ...formData, value: e.target.value })
                                }
                                placeholder={
                                  formData.type === 'percentage' ? 'VD: 18' : 'VD: 100000'
                                }
                              />
                            </div>
                            <div>
                              <Label htmlFor="edit-minAmount" className="pb-1">
                                Giá trị đơn hàng tối thiểu
                              </Label>
                              <Input
                                id="edit-minAmount"
                                type="number"
                                value={formData.minAmount}
                                onChange={(e) =>
                                  setFormData({ ...formData, minAmount: e.target.value })
                                }
                                placeholder="VD: 100000"
                              />
                            </div>
                            {formData.type === 'percentage' && (
                              <div>
                                <Label htmlFor="edit-maxDiscount" className="pb-1">
                                  Giảm tối đa
                                </Label>
                                <Input
                                  id="edit-maxDiscount"
                                  type="number"
                                  value={formData.maxDiscount}
                                  onChange={(e) =>
                                    setFormData({ ...formData, maxDiscount: e.target.value })
                                  }
                                  placeholder="VD: 250000"
                                />
                              </div>
                            )}
                            <div className="flex items-center space-x-2">
                              <Switch
                                id="edit-isActive"
                                checked={formData.isActive}
                                onCheckedChange={(checked) =>
                                  setFormData({ ...formData, isActive: checked })
                                }
                              />
                              <Label htmlFor="edit-isActive" className="pb-1">
                                Kích hoạt khuyến mãi
                              </Label>
                            </div>
                          </div>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button variant="outline" onClick={() => setEditingPromotion(null)}>
                                Hủy
                              </Button>
                            </DialogClose>
                            <Button
                              disabled={isUpdating}
                              onClick={async () => {
                                try {
                                  setIsUpdating(true);
                                  if (!editingPromotion) return;
                                  const token =
                                    typeof window !== 'undefined'
                                      ? localStorage.getItem('auth_token')
                                      : null;
                                  if (!token) {
                                    showErrorToast('Bạn chưa đăng nhập.');
                                    setIsUpdating(false);
                                    return;
                                  }
                                  const valueNum = Number(formData.value || 0);
                                  if (!valueNum || valueNum <= 0) {
                                    showErrorToast('Giá trị giảm phải lớn hơn 0.');
                                    setIsUpdating(false);
                                    return;
                                  }
                                  if (
                                    formData.type === 'percentage' &&
                                    (valueNum < 1 || valueNum > 100)
                                  ) {
                                    showErrorToast('Giá trị phần trăm phải trong khoảng 1-100.');
                                    setIsUpdating(false);
                                    return;
                                  }

                                  const payload: any = {
                                    name: formData.name,
                                    description: formData.description || null,
                                    type: formData.type,
                                    value: valueNum,
                                  };
                                  if (formData.minAmount)
                                    payload.min_amount = Number(formData.minAmount);
                                  if (formData.type === 'percentage') {
                                    payload.max_discount = formData.maxDiscount
                                      ? Number(formData.maxDiscount)
                                      : null;
                                  } else {
                                    payload.max_discount = null;
                                  }
                                  if (formData.validFrom)
                                    payload.valid_from = new Date(formData.validFrom).toISOString();
                                  if (formData.validTo)
                                    payload.valid_to = new Date(formData.validTo).toISOString();
                                  payload.is_active = Boolean(formData.isActive);

                                  const res = await fetch(
                                    `/api/promotions/${editingPromotion.id}`,
                                    {
                                      method: 'PATCH',
                                      headers: {
                                        'Content-Type': 'application/json',
                                        Authorization: `Bearer ${token}`,
                                      },
                                      body: JSON.stringify(payload),
                                    }
                                  );
                                  const data = await res.json().catch(() => ({}));
                                  if (res.ok && data && data.data) {
                                    const it = data.data;
                                    const updated: Promotion = {
                                      id: String(it.id ?? editingPromotion.id),
                                      code: it.code ?? editingPromotion.code,
                                      name: it.name ?? formData.name,
                                      description: it.description ?? '',
                                      type: (it.type === 'percentage'
                                        ? 'percentage'
                                        : 'fixed') as Promotion['type'],
                                      value: Number(it.value ?? valueNum),
                                      minAmount:
                                        it.min_amount != null ? Number(it.min_amount) : undefined,
                                      maxDiscount:
                                        it.max_discount != null
                                          ? Number(it.max_discount)
                                          : undefined,
                                      validFrom: it.valid_from ?? editingPromotion.validFrom,
                                      validTo: it.valid_to ?? editingPromotion.validTo,
                                      isActive: Boolean(it.is_active ?? formData.isActive),
                                    };

                                    setPromotions((prev) =>
                                      prev.map((p) => (p.id === editingPromotion.id ? updated : p))
                                    );
                                    setEditingPromotion(null); // will close dialog via onOpenChange
                                    showSuccessToast('Cập nhật khuyến mãi thành công');
                                  } else {
                                    showErrorToast(
                                      (data && (data.message || data.error)) ||
                                        'Cập nhật khuyến mãi thất bại'
                                    );
                                  }
                                } catch (e) {
                                  showErrorToast('Không thể kết nối tới máy chủ.');
                                } finally {
                                  setIsUpdating(false);
                                }
                              }}
                            >
                              {isUpdating ? 'Đang cập nhật...' : 'Cập nhật'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Xác nhận xóa khuyến mãi</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bạn có chắc chắn muốn xóa mã {promotion.code}? Hành động này không thể
                              hoàn tác.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Hủy</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={async () => {
                                try {
                                  const token =
                                    typeof window !== 'undefined'
                                      ? localStorage.getItem('auth_token')
                                      : null;
                                  if (!token) {
                                    showErrorToast('Bạn chưa đăng nhập.');
                                    return;
                                  }
                                  const res = await fetch(`/api/promotions/${promotion.id}`, {
                                    method: 'DELETE',
                                    headers: { Authorization: `Bearer ${token}` },
                                  });
                                  const data = await res.json().catch(() => ({}));
                                  if (res.ok && data && data.ok) {
                                    setPromotions((prev) =>
                                      prev.filter((p) => p.id !== promotion.id)
                                    );
                                    showSuccessToast('Xóa khuyến mãi thành công');
                                  } else {
                                    showErrorToast(
                                      (data && (data.message || data.error)) ||
                                        'Xóa khuyến mãi thất bại'
                                    );
                                  }
                                } catch (e) {
                                  showErrorToast('Không thể kết nối tới máy chủ.');
                                }
                              }}
                            >
                              Xóa
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
