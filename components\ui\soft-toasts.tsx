'use client';

import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

type StyleVars = React.CSSProperties;

const warningStyle: StyleVars = {
  '--normal-bg':
    'color-mix(in oklab, light-dark(var(--color-amber-600), var(--color-amber-400)) 10%, var(--background))',
  '--normal-text': 'light-dark(var(--color-amber-600), var(--color-amber-400))',
  '--normal-border': 'light-dark(var(--color-amber-600), var(--color-amber-400))',
} as React.CSSProperties;

const successStyle: StyleVars = {
  '--normal-bg':
    'color-mix(in oklab, light-dark(var(--color-green-600), var(--color-green-400)) 10%, var(--background))',
  '--normal-text': 'light-dark(var(--color-green-600), var(--color-green-400))',
  '--normal-border': 'light-dark(var(--color-green-600), var(--color-green-400))',
} as React.CSSProperties;

const errorStyle: StyleVars = {
  '--normal-bg': 'color-mix(in oklab, var(--destructive) 10%, var(--background))',
  '--normal-text': 'var(--destructive)',
  '--normal-border': 'var(--destructive)',
} as React.CSSProperties;

const infoStyle: StyleVars = {
  '--normal-bg':
    'color-mix(in oklab, light-dark(var(--color-sky-600), var(--color-sky-400)) 10%, var(--background))',
  '--normal-text': 'light-dark(var(--color-sky-600), var(--color-sky-400))',
  '--normal-border': 'light-dark(var(--color-sky-600), var(--color-sky-400))',
} as React.CSSProperties;

export function showWarningToast(message = 'Warning: Please check the entered data.') {
  toast.warning(message, { style: warningStyle });
}

export function showSuccessToast(message = 'Action completed successfully!') {
  toast.success(message, { style: successStyle });
}

export function showErrorToast(message = 'Oops, there was an error processing your request.') {
  toast.error(message, { style: errorStyle });
}

export function showInfoToast(message = 'This is for your information, please note.') {
  toast.info(message, { style: infoStyle });
}

// Optional small demo components for each toast (kept minimal)
export function WarningToastButton() {
  return (
    <Button variant="outline" onClick={() => showWarningToast()}>
      Soft Warning Toast
    </Button>
  );
}

export function SuccessToastButton() {
  return (
    <Button variant="outline" onClick={() => showSuccessToast()}>
      Soft Success Toast
    </Button>
  );
}

export function ErrorToastButton() {
  return (
    <Button variant="outline" onClick={() => showErrorToast()}>
      Soft Destructive Toast
    </Button>
  );
}

export function InfoToastButton() {
  return (
    <Button variant="outline" onClick={() => showInfoToast()}>
      Soft Info Toast
    </Button>
  );
}

export default {
  showWarningToast,
  showSuccessToast,
  showErrorToast,
  showInfoToast,
};
