'use client';

import React from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { showErrorToast, showSuccessToast } from '@/components/ui/soft-toasts';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { Port, Route } from '@/lib/types';
import { Edit, MapPin, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

export default function PortsPage() {
  const [ports, setPorts] = useState<Port[]>([]);
  const [routes, setRoutes] = useState<Route[]>([]);
  const [isLoadingPorts, setIsLoadingPorts] = useState(false);
  const [isSubmittingRoute, setIsSubmittingRoute] = useState(false);
  const [isPortDialogOpen, setIsPortDialogOpen] = useState(false);
  const [isRouteDialogOpen, setIsRouteDialogOpen] = useState(false);
  const [editingPort, setEditingPort] = useState<Port | null>(null);
  const [editingRoute, setEditingRoute] = useState<Route | null>(null);
  const [isSubmittingPort, setIsSubmittingPort] = useState(false);

  const [portForm, setPortForm] = useState({
    name: '',
    code: '',
    city: '',
    address: '',
    lat: '',
    lng: '',
  });

  const [routeForm, setRouteForm] = useState({
    fromPortId: '',
    toPortId: '',
    distance: '',
    estimatedDuration: '',
  });

  const handlePortSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmittingPort(true);
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      if (!token) {
        showErrorToast('Bạn chưa đăng nhập.');
        setIsSubmittingPort(false);
        return;
      }

      const payload = {
        id: editingPort?.id || portForm.code.toUpperCase(),
        name: portForm.name,
        code: portForm.code.toUpperCase(),
        city: portForm.city,
        address: portForm.address,
        latitude: Number.parseFloat(portForm.lat),
        longitude: Number.parseFloat(portForm.lng),
      };

      const isEdit = Boolean(editingPort);
      const endpoint = isEdit ? `/api/ports/${encodeURIComponent(editingPort!.id)}` : '/api/ports';
      const method = isEdit ? 'PATCH' : 'POST';

      const res = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: JSON.stringify(
          isEdit
            ? {
                name: payload.name,
                code: payload.code,
                city: payload.city,
                address: payload.address,
                latitude: payload.latitude,
                longitude: payload.longitude,
              }
            : payload
        ),
      });
      const data = await res.json().catch(() => ({}));
      if (res.ok && data && data.data) {
        const it = data.data;
        const newPort: Port = {
          id: String(it.id ?? payload.id),
          name: it.name ?? payload.name,
          code: it.code ?? payload.code,
          city: it.city ?? payload.city,
          address: it.address ?? payload.address,
          coordinates: {
            lat: Number(it.latitude ?? payload.latitude),
            lng: Number(it.longitude ?? payload.longitude),
          },
        };
        if (isEdit) {
          setPorts((prev) => prev.map((p) => (p.id === newPort.id ? newPort : p)));
          showSuccessToast('Cập nhật cảng thành công');
        } else {
          setPorts((prev) => [newPort, ...prev]);
          showSuccessToast('Thêm cảng thành công');
        }
        setIsPortDialogOpen(false);
        setEditingPort(null);
        setPortForm({ name: '', code: '', city: '', address: '', lat: '', lng: '' });
      } else {
        showErrorToast(
          (data && (data.message || data.error)) ||
            (isEdit ? 'Cập nhật cảng thất bại' : 'Tạo cảng thất bại')
        );
      }
    } catch {
      showErrorToast('Không thể kết nối tới máy chủ.');
    } finally {
      setIsSubmittingPort(false);
    }
  };

  // Fetch ports from API
  React.useEffect(() => {
    const fetchPorts = async () => {
      try {
        setIsLoadingPorts(true);
        const res = await fetch('/api/ports', { cache: 'no-store' });
        const data = await res.json().catch(() => ({}));
        if (res.ok) {
          const items = (data && (data.data?.items || data.items || data.data || data)) || [];
          const mapped: Port[] = Array.isArray(items)
            ? items.map((it: any) => ({
                id: String(it.id ?? ''),
                name: it.name ?? '',
                code: it.code ?? '',
                city: it.city ?? '',
                address: it.address ?? '',
                coordinates: {
                  lat: Number(it.latitude ?? it.lat ?? 0),
                  lng: Number(it.longitude ?? it.lng ?? 0),
                },
              }))
            : [];
          setPorts(mapped);
        } else {
          showErrorToast((data && (data.message || data.error)) || 'Không tải được danh sách cảng');
        }
      } catch {
        showErrorToast('Không thể kết nối tới máy chủ.');
      } finally {
        setIsLoadingPorts(false);
      }
    };
    fetchPorts();
  }, []);

  // Fetch routes from API (database)
  React.useEffect(() => {
    const fetchRoutes = async () => {
      try {
        const res = await fetch('/api/routes', { cache: 'no-store' });
        const data = await res.json().catch(() => ({}));
        if (res.ok) {
          const items = (data && (data.data?.items || data.items || data.data || data)) || [];
          const mapped: Route[] = Array.isArray(items)
            ? items.map((it: any) => ({
                id: String(it.id ?? ''),
                fromPortId: it.from_port_id ?? it.fromPortId ?? '',
                toPortId: it.to_port_id ?? it.toPortId ?? '',
                distance: Number(it.distance_km ?? it.distance ?? 0),
                estimatedDuration: Number(
                  it.estimated_duration_minutes ?? it.estimatedDuration ?? 0
                ),
              }))
            : [];
          setRoutes(mapped);
        } else {
          showErrorToast(
            (data && (data.message || data.error)) || 'Không tải được danh sách tuyến'
          );
        }
      } catch {
        showErrorToast('Không thể kết nối tới máy chủ.');
      }
    };
    fetchRoutes();
  }, []);

  const handleRouteSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmittingRoute(true);
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      if (!token) {
        showErrorToast('Bạn chưa đăng nhập.');
        setIsSubmittingRoute(false);
        return;
      }

      const payload = {
        id: editingRoute?.id || `R_${routeForm.fromPortId}_${routeForm.toPortId}`,
        from_port_id: routeForm.fromPortId,
        to_port_id: routeForm.toPortId,
        distance_km: Number(routeForm.distance),
        estimated_duration_minutes: Number(routeForm.estimatedDuration),
      };

      const isEdit = Boolean(editingRoute);
      const endpoint = isEdit
        ? `/api/routes/${encodeURIComponent(editingRoute!.id)}`
        : '/api/routes';
      const method = isEdit ? 'PATCH' : 'POST';

      const res = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: JSON.stringify(
          isEdit ? { estimated_duration_minutes: payload.estimated_duration_minutes } : payload
        ),
      });
      const data = await res.json().catch(() => ({}));
      if (res.ok && data && data.data) {
        const it = data.data;
        const newRoute: Route = {
          id: String(it.id ?? payload.id),
          fromPortId: it.from_port_id ?? payload.from_port_id,
          toPortId: it.to_port_id ?? payload.to_port_id,
          distance: Number(it.distance_km ?? payload.distance_km),
          estimatedDuration: Number(
            it.estimated_duration_minutes ?? payload.estimated_duration_minutes
          ),
        };
        if (isEdit) {
          setRoutes((prev) => prev.map((r) => (r.id === newRoute.id ? newRoute : r)));
          showSuccessToast('Cập nhật tuyến thành công');
        } else {
          setRoutes((prev) => [newRoute, ...prev]);
          showSuccessToast('Thêm tuyến thành công');
        }
        setIsRouteDialogOpen(false);
        setEditingRoute(null);
        setRouteForm({ fromPortId: '', toPortId: '', distance: '', estimatedDuration: '' });
      } else {
        showErrorToast(
          (data && (data.message || data.error)) ||
            (isEdit ? 'Cập nhật tuyến thất bại' : 'Thêm tuyến thất bại')
        );
      }
    } catch {
      showErrorToast('Không thể kết nối tới máy chủ.');
    } finally {
      setIsSubmittingRoute(false);
    }
  };

  const handleEditPort = (port: Port) => {
    setEditingPort(port);
    setPortForm({
      name: port.name,
      code: port.code,
      city: port.city,
      address: port.address,
      lat: port.coordinates.lat.toString(),
      lng: port.coordinates.lng.toString(),
    });
    setIsPortDialogOpen(true);
  };

  const handleEditRoute = (route: Route) => {
    setEditingRoute(route);
    setRouteForm({
      fromPortId: route.fromPortId,
      toPortId: route.toPortId,
      distance: route.distance.toString(),
      estimatedDuration: route.estimatedDuration.toString(),
    });
    setIsRouteDialogOpen(true);
  };

  const handleDeletePort = async (portId: string) => {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      if (!token) {
        showErrorToast('Bạn chưa đăng nhập.');
        return;
      }
      const res = await fetch(`/api/ports/${encodeURIComponent(portId)}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await res.json().catch(() => ({}));
      if (res.ok && data && data.ok) {
        setPorts((prev) => prev.filter((p) => p.id !== portId));
        showSuccessToast('Xóa cảng thành công');
      } else {
        showErrorToast((data && (data.message || data.error)) || 'Xóa cảng thất bại');
      }
    } catch {
      showErrorToast('Không thể kết nối tới máy chủ.');
    }
  };

  const handleDeleteRoute = async (routeId: string) => {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      if (!token) {
        showErrorToast('Bạn chưa đăng nhập.');
        return;
      }
      const res = await fetch(`/api/routes/${encodeURIComponent(routeId)}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await res.json().catch(() => ({}));
      if (res.ok && data && data.ok) {
        setRoutes((prev) => prev.filter((r) => r.id !== routeId));
        showSuccessToast('Xóa tuyến thành công');
      } else {
        showErrorToast((data && (data.message || data.error)) || 'Xóa tuyến thất bại');
      }
    } catch {
      showErrorToast('Không thể kết nối tới máy chủ.');
    }
  };

  const getPortName = (portId: string) => {
    return ports.find((p) => p.id === portId)?.name || 'Unknown';
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Cảng & Tuyến đường</h1>
        <p className="text-gray-600">Quản lý cảng tàu và tuyến đường</p>
      </div>

      {/* Ports Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Danh sách cảng</span>
            </CardTitle>
            <Dialog open={isPortDialogOpen} onOpenChange={setIsPortDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm cảng
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{editingPort ? 'Sửa cảng' : 'Thêm cảng mới'}</DialogTitle>
                </DialogHeader>
                <form onSubmit={handlePortSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="port-name" className="pb-1">
                        Tên cảng
                      </Label>
                      <Input
                        id="port-name"
                        value={portForm.name}
                        onChange={(e) => setPortForm({ ...portForm, name: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="port-code" className="pb-1">
                        Mã cảng
                      </Label>
                      <Input
                        id="port-code"
                        value={portForm.code}
                        onChange={(e) => setPortForm({ ...portForm, code: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="port-city" className="pb-1">
                      Thành phố
                    </Label>
                    <Input
                      id="port-city"
                      value={portForm.city}
                      onChange={(e) => setPortForm({ ...portForm, city: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="port-address" className="pb-1">
                      Địa chỉ
                    </Label>
                    <Input
                      id="port-address"
                      value={portForm.address}
                      onChange={(e) => setPortForm({ ...portForm, address: e.target.value })}
                      required
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="port-lat" className="pb-1">
                        Vĩ độ
                      </Label>
                      <Input
                        id="port-lat"
                        type="number"
                        step="any"
                        value={portForm.lat}
                        onChange={(e) => setPortForm({ ...portForm, lat: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="port-lng" className="pb-1">
                        Kinh độ
                      </Label>
                      <Input
                        id="port-lng"
                        type="number"
                        step="any"
                        value={portForm.lng}
                        onChange={(e) => setPortForm({ ...portForm, lng: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsPortDialogOpen(false)}
                    >
                      Hủy
                    </Button>
                    <Button type="submit" disabled={isSubmittingPort}>
                      {editingPort
                        ? isSubmittingPort
                          ? 'Đang cập nhật...'
                          : 'Cập nhật'
                        : isSubmittingPort
                        ? 'Đang thêm...'
                        : 'Thêm'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên cảng</TableHead>
                <TableHead>Mã</TableHead>
                <TableHead>Thành phố</TableHead>
                <TableHead>Địa chỉ</TableHead>
                <TableHead>Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {ports.map((port) => (
                <TableRow key={port.id}>
                  <TableCell className="font-medium">{port.name}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{port.code}</Badge>
                  </TableCell>
                  <TableCell>{port.city}</TableCell>
                  <TableCell>{port.address}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleEditPort(port)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Xóa cảng này?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bạn sắp xóa cảng <span className="font-medium">{port.name}</span> (
                              {port.code}). Hành động này không thể hoàn tác.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Hủy</AlertDialogCancel>
                            <AlertDialogAction
                              className="bg-red-600 hover:bg-red-700 text-white"
                              onClick={() => handleDeletePort(port.id)}
                            >
                              Xóa
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Routes Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Tuyến đường</CardTitle>
            <Dialog open={isRouteDialogOpen} onOpenChange={setIsRouteDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm tuyến
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{editingRoute ? 'Sửa tuyến' : 'Thêm tuyến mới'}</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleRouteSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="from-port" className="pb-1">
                        Cảng đi
                      </Label>
                      <select
                        id="from-port"
                        className="w-full p-2 border rounded-md"
                        value={routeForm.fromPortId}
                        onChange={(e) => setRouteForm({ ...routeForm, fromPortId: e.target.value })}
                        required
                      >
                        <option value="">Chọn cảng đi</option>
                        {ports.map((port) => (
                          <option key={port.id} value={port.id}>
                            {port.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="to-port" className="pb-1">
                        Cảng đến
                      </Label>
                      <select
                        id="to-port"
                        className="w-full p-2 border rounded-md"
                        value={routeForm.toPortId}
                        onChange={(e) => setRouteForm({ ...routeForm, toPortId: e.target.value })}
                        required
                      >
                        <option value="">Chọn cảng đến</option>
                        {ports.map((port) => (
                          <option key={port.id} value={port.id}>
                            {port.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="distance" className="pb-1">
                        Khoảng cách (km)
                      </Label>
                      <Input
                        id="distance"
                        type="number"
                        value={routeForm.distance}
                        onChange={(e) => setRouteForm({ ...routeForm, distance: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="duration" className="pb-1">
                        Thời gian (phút)
                      </Label>
                      <Input
                        id="duration"
                        type="number"
                        value={routeForm.estimatedDuration}
                        onChange={(e) =>
                          setRouteForm({ ...routeForm, estimatedDuration: e.target.value })
                        }
                        required
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsRouteDialogOpen(false)}
                    >
                      Hủy
                    </Button>
                    <Button type="submit" disabled={isSubmittingRoute}>
                      {editingRoute
                        ? isSubmittingRoute
                          ? 'Đang cập nhật...'
                          : 'Cập nhật'
                        : isSubmittingRoute
                        ? 'Đang thêm...'
                        : 'Thêm'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tuyến</TableHead>
                <TableHead>Khoảng cách</TableHead>
                <TableHead>Thời gian</TableHead>
                <TableHead>Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {routes.map((route) => (
                <TableRow key={route.id}>
                  <TableCell className="font-medium">
                    {getPortName(route.fromPortId)} → {getPortName(route.toPortId)}
                  </TableCell>
                  <TableCell>{route.distance} km</TableCell>
                  <TableCell>
                    {Math.floor(route.estimatedDuration / 60)}h {route.estimatedDuration % 60}m
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleEditRoute(route)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Xóa tuyến đường?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bạn sắp xóa tuyến{' '}
                              <span className="font-medium">{getPortName(route.fromPortId)}</span> →{' '}
                              <span className="font-medium">{getPortName(route.toPortId)}</span>.
                              Hành động này không thể hoàn tác.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Hủy</AlertDialogCancel>
                            <AlertDialogAction
                              className="bg-red-600 hover:bg-red-700 text-white"
                              onClick={() => handleDeleteRoute(route.id)}
                            >
                              Xóa
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
