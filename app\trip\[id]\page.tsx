'use client';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { BookingSummary } from '@/components/trip/booking-summary';
import { SeatMap } from '@/components/trip/seat-map';
import { TripInfo } from '@/components/trip/trip-info';
import { Button } from '@/components/ui/button';
import {
  getPortById,
  getRouteById,
  getScheduleById,
  getSeatMapById,
  getVesselById,
} from '@/lib/mock-data';
import type {
  PassengerType,
  Port,
  Route,
  Schedule,
  Seat,
  SeatMap as SeatMapType,
  Vessel,
} from '@/lib/types';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

interface SelectedSeat {
  seat: Seat;
  passengerType: PassengerType;
  passengerIndex: number;
}

export default function TripDetailsPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const scheduleId = params.id as string;

  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [route, setRoute] = useState<Route | null>(null);
  const [vessel, setVessel] = useState<Vessel | null>(null);
  const [seatMap, setSeatMap] = useState<SeatMapType | null>(null);
  const [fromPort, setFromPort] = useState<Port | null>(null);
  const [toPort, setToPort] = useState<Port | null>(null);
  const [selectedSeats, setSelectedSeats] = useState<SelectedSeat[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Parse search parameters
  const searchQuery = {
    fromPortId: searchParams.get('from') || '',
    toPortId: searchParams.get('to') || '',
    departureDate: searchParams.get('departure') || '',
    returnDate: searchParams.get('return') || '',
    tripType: (searchParams.get('type') as 'one-way' | 'round-trip') || 'one-way',
    adultCount: Number.parseInt(searchParams.get('adults') || '1'),
    childCount: Number.parseInt(searchParams.get('children') || '0'),
  };

  const totalPassengers = searchQuery.adultCount + searchQuery.childCount;

  useEffect(() => {
    const scheduleData = getScheduleById(scheduleId);
    if (scheduleData) {
      const routeData = getRouteById(scheduleData.routeId);
      const vesselData = getVesselById(scheduleData.vesselId);
      const seatMapData = getSeatMapById(vesselData?.seatMapId || '');
      const fromPortData = getPortById(routeData?.fromPortId || '');
      const toPortData = getPortById(routeData?.toPortId || '');

      setSchedule(scheduleData);
      setRoute(routeData || null);
      setVessel(vesselData || null);
      setSeatMap(seatMapData || null);
      setFromPort(fromPortData || null);
      setToPort(toPortData || null);
    }
    setIsLoading(false);
  }, [scheduleId]);

  const handleSeatSelect = (seat: Seat) => {
    if (seat.status !== 'available') return;

    // Check if seat is already selected
    const existingIndex = selectedSeats.findIndex((s) => s.seat.id === seat.id);
    if (existingIndex >= 0) {
      // Deselect seat
      setSelectedSeats(selectedSeats.filter((_, index) => index !== existingIndex));
      return;
    }

    // Check if we can select more seats
    if (selectedSeats.length >= totalPassengers) {
      return;
    }

    // Determine passenger type for this seat
    const adultSeatsSelected = selectedSeats.filter((s) => s.passengerType === 'adult').length;
    const passengerType: PassengerType =
      adultSeatsSelected < searchQuery.adultCount ? 'adult' : 'child';
    const passengerIndex =
      passengerType === 'adult'
        ? adultSeatsSelected
        : selectedSeats.filter((s) => s.passengerType === 'child').length;

    setSelectedSeats([...selectedSeats, { seat, passengerType, passengerIndex }]);
  };

  const calculateTotal = () => {
    return selectedSeats.reduce((total, { seat, passengerType }) => {
      return total + seat.price[passengerType];
    }, 0);
  };

  const backUrl = `/search/results?${new URLSearchParams({
    from: searchQuery.fromPortId,
    to: searchQuery.toPortId,
    departure: searchQuery.departureDate,
    adults: searchQuery.adultCount.toString(),
    children: searchQuery.childCount.toString(),
    type: searchQuery.tripType,
    ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
  }).toString()}`;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded mb-4"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!schedule || !route || !vessel || !seatMap || !fromPort || !toPort) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Không tìm thấy chuyến</h1>
            <Link href="/">
              <Button>Quay lại trang chủ</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar04Page />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
        {/* Back Button */}
        <Link href={backUrl}>
          <Button variant="ghost" className="mb-6">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại kết quả tìm kiếm
          </Button>
        </Link>

        {/* Trip Info */}
        <TripInfo
          schedule={schedule}
          route={route}
          vessel={vessel}
          fromPort={fromPort}
          toPort={toPort}
          searchQuery={searchQuery}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Seat Map */}
          <div className="lg:col-span-2">
            <SeatMap
              seatMap={seatMap}
              selectedSeats={selectedSeats}
              onSeatSelect={handleSeatSelect}
              totalPassengers={totalPassengers}
            />
          </div>

          {/* Booking Summary */}
          <div className="lg:col-span-1">
            <BookingSummary
              schedule={schedule}
              fromPort={fromPort}
              toPort={toPort}
              selectedSeats={selectedSeats}
              searchQuery={searchQuery}
              totalAmount={calculateTotal()}
            />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
