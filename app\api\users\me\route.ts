import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const apiBaseUrl = process.env.API_URL;
    if (!apiBaseUrl) {
      return NextResponse.json(
        { ok: false, error: 'Server misconfiguration: API_URL is not set' },
        { status: 500 }
      );
    }

    const authHeader = request.headers.get('authorization') || request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { ok: false, error: 'Missing Authorization header' },
        { status: 401 }
      );
    }

    const response = await fetch(`${apiBaseUrl}/api/users/me`, {
      method: 'GET',
      headers: { Authorization: authHeader },
    });

    const text = await response.text();
    let data: unknown;
    try {
      data = text ? JSON.parse(text) : {};
    } catch {
      data = { message: text };
    }
    return NextResponse.json(data as any, { status: response.status });
  } catch (error) {
    return NextResponse.json({ ok: false, error: 'Unable to fetch user info' }, { status: 502 });
  }
}

export async function PATCH(request: Request) {
  try {
    const apiBaseUrl = process.env.API_URL;
    if (!apiBaseUrl) {
      return NextResponse.json(
        { ok: false, error: 'Server misconfiguration: API_URL is not set' },
        { status: 500 }
      );
    }

    const authHeader = request.headers.get('authorization') || request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { ok: false, error: 'Missing Authorization header' },
        { status: 401 }
      );
    }

    const body = await request.text();

    const response = await fetch(`${apiBaseUrl}/api/users/me`, {
      method: 'PATCH',
      headers: {
        Authorization: authHeader,
        'Content-Type': 'application/json',
      },
      body,
    });

    const text = await response.text();
    let data: unknown;
    try {
      data = text ? JSON.parse(text) : {};
    } catch {
      data = { message: text };
    }
    return NextResponse.json(data as any, { status: response.status });
  } catch (error) {
    return NextResponse.json({ ok: false, error: 'Unable to update user' }, { status: 502 });
  }
}
