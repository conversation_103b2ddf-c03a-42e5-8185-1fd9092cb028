'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { getScheduleById, getUserById, mockBookings, mockVessels } from '@/lib/mock-data';
import { BarChart3, Calendar, DollarSign, Download, Ship, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';

export default function ReportsPage() {
  const [timeRange, setTimeRange] = useState('month');
  const [reportType, setReportType] = useState('revenue');

  // Calculate statistics
  const totalRevenue = mockBookings
    .filter((booking) => booking.status === 'confirmed')
    .reduce((total, booking) => total + booking.totalAmount, 0);

  const totalBookings = mockBookings.length;
  const totalPassengers = mockBookings.reduce(
    (total, booking) => total + booking.passengers.length,
    0
  );
  const averageBookingValue = totalRevenue / totalBookings;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  // Revenue by vessel
  const revenueByVessel = mockVessels
    .map((vessel) => {
      const vesselBookings = mockBookings.filter((booking) => {
        const schedule = getScheduleById(booking.outboundScheduleId);
        return schedule?.vesselId === vessel.id && booking.status === 'confirmed';
      });
      const revenue = vesselBookings.reduce((total, booking) => total + booking.totalAmount, 0);
      const bookingCount = vesselBookings.length;
      return {
        vessel,
        revenue,
        bookingCount,
        passengerCount: vesselBookings.reduce(
          (total, booking) => total + booking.passengers.length,
          0
        ),
      };
    })
    .sort((a, b) => b.revenue - a.revenue);

  // Recent bookings
  const recentBookings = mockBookings
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 10);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Báo cáo & Thống kê</h1>
          <p className="text-gray-600">Phân tích doanh thu và hiệu suất kinh doanh</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">7 ngày qua</SelectItem>
              <SelectItem value="month">30 ngày qua</SelectItem>
              <SelectItem value="quarter">3 tháng qua</SelectItem>
              <SelectItem value="year">12 tháng qua</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Xuất báo cáo
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng doanh thu</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              +12.5% so với tháng trước
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng đơn đặt vé</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBookings}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              +8.2% so với tháng trước
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng hành khách</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPassengers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              +15.3% so với tháng trước
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Giá trị TB/đơn</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(averageBookingValue)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              +3.7% so với tháng trước
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue by Vessel */}
        <Card>
          <CardHeader>
            <CardTitle>Doanh thu theo tàu</CardTitle>
            <CardDescription>Hiệu suất kinh doanh của từng tàu</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tàu</TableHead>
                  <TableHead>Đơn hàng</TableHead>
                  <TableHead>Hành khách</TableHead>
                  <TableHead>Doanh thu</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {revenueByVessel.map((item) => (
                  <TableRow key={item.vessel.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div>{item.vessel.name}</div>
                        <div className="text-sm text-muted-foreground">{item.vessel.code}</div>
                      </div>
                    </TableCell>
                    <TableCell>{item.bookingCount}</TableCell>
                    <TableCell>{item.passengerCount}</TableCell>
                    <TableCell className="font-medium">{formatCurrency(item.revenue)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Recent Bookings */}
        <Card>
          <CardHeader>
            <CardTitle>Đơn đặt vé gần đây</CardTitle>
            <CardDescription>10 đơn đặt vé mới nhất</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Mã đơn</TableHead>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Số tiền</TableHead>
                  <TableHead>Trạng thái</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentBookings.map((booking) => {
                  const user = booking.userId ? getUserById(booking.userId) : undefined;
                  return (
                    <TableRow key={booking.id}>
                      <TableCell className="font-medium">{booking.code}</TableCell>
                      <TableCell>
                        <div>
                          <div>{user?.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(booking.createdAt)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(booking.totalAmount)}</TableCell>
                      <TableCell>
                        <Badge
                          variant={booking.status === 'confirmed' ? 'default' : 'secondary'}
                          className={
                            booking.status === 'confirmed' ? 'bg-green-100 text-green-800' : ''
                          }
                        >
                          {booking.status === 'confirmed'
                            ? 'Đã xác nhận'
                            : booking.status === 'pending'
                            ? 'Chờ xử lý'
                            : booking.status === 'cancelled'
                            ? 'Đã hủy'
                            : booking.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Additional Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Ship className="h-5 w-5 mr-2" />
              Tỷ lệ lấp đầy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78.5%</div>
            <p className="text-sm text-muted-foreground">Trung bình tháng này</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Chuyến đi hoàn thành
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-sm text-muted-foreground">Trong tháng này</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Khách hàng mới
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-sm text-muted-foreground">Trong tuần này</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
