'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { BookingDialog } from '@/components/ui/booking-dialog';

interface CardProductProps {
  image?: string;
  imageAlt?: string;
  title?: string;
  badges?: string[];
  description?: string;
  priceLabel?: string;
  price?: string;
  buttonText?: string;
  gradientClasses?: string;
  onButtonClick?: () => void;
}

const CardProduct = ({
  image = 'https://cdn.shadcnstudio.com/ss-assets/components/card/image-11.png?width=300&format=auto',
  imageAlt = 'Shoes',
  title = 'Nike Jordan Air Rev',
  badges = ['EU38', 'Black and White'],
  description = "Crossing hardwood comfort with off-court flair. '80s-Inspired construction, bold details and nothin'-but-net style.",
  priceLabel = 'Price',
  price = '$69.99',
  buttonText = 'Add to cart',
  gradientClasses = 'bg-gradient-to-r from-zinc-600 to-violet-300',
  onButtonClick,
}: CardProductProps) => {
  return (
    <div className={`relative max-w-md rounded-xl ${gradientClasses} pt-0 shadow-lg`}>
      <div className="flex h-60 items-center justify-center overflow-hidden rounded-t-xl">
        <img src={image} alt={imageAlt} className="w-full h-full object-cover" />
      </div>
      <Card className="border-none -mt-3 relative z-[2]">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription className="flex items-center gap-2 mt-2">
            {badges.map((badge, index) => (
              <Badge key={index} variant="outline">
                {badge}
              </Badge>
            ))}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>{description}</p>
        </CardContent>
        <CardFooter className="justify-between gap-3 max-sm:flex-col max-sm:items-stretch">
          <div className="flex flex-col">
            <span className="text-sm font-medium uppercase">{priceLabel}</span>
            <span className="text-xl font-semibold">{price}</span>
          </div>
          <BookingDialog 
            tripTitle={title}
            tripPrice={price}
            buttonText={buttonText}
            image={image}
          />
        </CardFooter>
      </Card>
    </div>
  );
};

export default CardProduct;
