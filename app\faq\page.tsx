'use client';

import StartChatButton from '@/components/app/faq/chat';
import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Clock,
  CreditCard,
  Mail,
  MapPin,
  MessageCircle,
  Phone,
  Ship,
  Ticket,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export default function FAQPage() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const faqCategories = [
    {
      title: 'Đặt vé',
      icon: <Ticket className="h-5 w-5" />,
      questions: [
        {
          question: '<PERSON><PERSON><PERSON> thế nào để đặt vé tàu?',
          answer:
            '<PERSON><PERSON><PERSON> có thể đặt vé trực tuyến bằng cách: 1) Chọn điểm đi và điểm đến, 2) <PERSON>ọn ngày khởi hành, 3) Chọn số lượng hành khách, 4) Chọn chỗ ngồi trên sơ đồ tàu, 5) Điền thông tin hành khách và thanh toán.',
        },
        {
          question: 'Tôi có thể đặt vé khứ hồi không?',
          answer:
            "Có, hệ thống hỗ trợ đặt vé khứ hồi. Bạn chỉ cần chọn 'Khứ hồi' khi tìm kiếm và chọn cả chuyến đi và chuyến về.",
        },
        {
          question: 'Có thể đặt vé cho trẻ em không?',
          answer:
            'Có, trẻ em từ 2-12 tuổi được giảm 50% giá vé. Trẻ em dưới 2 tuổi đi cùng người lớn miễn phí (không có ghế riêng).',
        },
        {
          question: 'Tôi có thể chọn chỗ ngồi không?',
          answer:
            'Có, sau khi chọn chuyến tàu, bạn sẽ thấy sơ đồ tàu với các loại ghế khác nhau (Phổ thông, Thương gia, VIP, Cabin). Bạn có thể chọn chỗ ngồi yêu thích.',
        },
      ],
    },
    {
      title: 'Thanh toán',
      icon: <CreditCard className="h-5 w-5" />,
      questions: [
        {
          question: 'Có những phương thức thanh toán nào?',
          answer:
            'Chúng tôi hỗ trợ thanh toán qua: Thẻ tín dụng/ghi nợ (Visa, Mastercard), Ví điện tử (MoMo, ZaloPay, VNPay), Chuyển khoản ngân hàng.',
        },
        {
          question: 'Thanh toán có an toàn không?',
          answer:
            'Có, tất cả giao dịch được mã hóa SSL 256-bit và tuân thủ tiêu chuẩn bảo mật PCI DSS. Thông tin thẻ của bạn không được lưu trữ trên hệ thống.',
        },
        {
          question: 'Tôi có thể sử dụng mã giảm giá không?',
          answer:
            'Có, bạn có thể nhập mã giảm giá tại trang thanh toán. Mã giảm giá có thể áp dụng theo phần trăm hoặc số tiền cố định.',
        },
        {
          question: 'Khi nào tiền sẽ được trừ từ tài khoản?',
          answer:
            'Tiền sẽ được trừ ngay sau khi bạn xác nhận thanh toán thành công. Bạn sẽ nhận được email xác nhận và vé điện tử.',
        },
      ],
    },
    {
      title: 'Vé điện tử',
      icon: <Ticket className="h-5 w-5" />,
      questions: [
        {
          question: 'Làm thế nào để nhận vé sau khi đặt?',
          answer:
            "Sau khi thanh toán thành công, vé điện tử sẽ được gửi qua email. Bạn cũng có thể xem và tải vé trong mục 'Đơn đặt vé' trên tài khoản.",
        },
        {
          question: 'Vé điện tử có mã QR để làm gì?',
          answer:
            'Mã QR trên vé điện tử dùng để check-in tại cảng. Nhân viên sẽ quét mã này để xác nhận thông tin vé và cho bạn lên tàu.',
        },
        {
          question: 'Tôi có thể in vé giấy không?',
          answer:
            'Có, bạn có thể tải vé dưới dạng PDF và in ra. Tuy nhiên, vé điện tử trên điện thoại cũng được chấp nhận tại tất cả cảng.',
        },
        {
          question: 'Nếu mất vé điện tử thì làm sao?',
          answer:
            'Bạn có thể đăng nhập vào tài khoản để xem lại vé hoặc liên hệ hotline với mã đặt vé để được hỗ trợ in lại vé.',
        },
      ],
    },
    {
      title: 'Hủy vé & Đổi lịch',
      icon: <Clock className="h-5 w-5" />,
      questions: [
        {
          question: 'Tôi có thể hủy vé không?',
          answer:
            'Có, bạn có thể hủy vé trước giờ khởi hành ít nhất 2 tiếng. Phí hủy vé: Trước 24h: miễn phí, 2-24h trước: 10% giá vé, dưới 2h: không được hủy.',
        },
        {
          question: 'Làm thế nào để đổi lịch chuyến tàu?',
          answer:
            "Bạn có thể đổi lịch trong mục 'Đơn đặt vé' hoặc liên hệ hotline. Phí đổi lịch: 50,000 VNĐ/vé + chênh lệch giá vé (nếu có).",
        },
        {
          question: 'Khi nào tiền hoàn sẽ được trả lại?',
          answer:
            'Tiền hoàn sẽ được chuyển về tài khoản/thẻ gốc trong vòng 3-7 ngày làm việc sau khi xử lý hủy vé thành công.',
        },
        {
          question: 'Có thể hủy một phần vé trong đơn hàng không?',
          answer:
            'Có, bạn có thể hủy từng vé riêng lẻ trong đơn hàng. Các vé còn lại vẫn có hiệu lực bình thường.',
        },
      ],
    },
    {
      title: 'Tại cảng',
      icon: <Ship className="h-5 w-5" />,
      questions: [
        {
          question: 'Tôi cần có mặt tại cảng trước bao lâu?',
          answer:
            'Bạn nên có mặt tại cảng trước giờ khởi hành ít nhất 30 phút để làm thủ tục check-in và lên tàu.',
        },
        {
          question: 'Cần mang theo giấy tờ gì?',
          answer:
            'Bạn cần mang theo CMND/CCCD hoặc hộ chiếu (bản gốc) và vé điện tử. Trẻ em cần có giấy khai sinh hoặc CMND.',
        },
        {
          question: 'Có dịch vụ gửi xe tại cảng không?',
          answer:
            'Có, tất cả cảng đều có bãi gửi xe máy và ô tô với phí theo giờ. Bạn nên đến sớm để tìm chỗ đậu xe.',
        },
        {
          question: 'Nếu đến muộn thì có được lên tàu không?',
          answer:
            'Tàu sẽ khởi hành đúng giờ. Nếu bạn đến sau giờ khởi hành, vé sẽ bị hủy và không được hoàn tiền.',
        },
      ],
    },
    {
      title: 'Hành lý',
      icon: <Users className="h-5 w-5" />,
      questions: [
        {
          question: 'Quy định về hành lý như thế nào?',
          answer:
            'Mỗi hành khách được mang tối đa 20kg hành lý miễn phí. Hành lý quá cân sẽ tính phí 10,000 VNĐ/kg.',
        },
        {
          question: 'Có những vật phẩm nào bị cấm mang lên tàu?',
          answer:
            'Cấm mang: Chất dễ cháy nổ, vũ khí, ma túy, động vật sống (trừ thú cưng có giấy tờ), thực phẩm có mùi nặng.',
        },
        {
          question: 'Có thể mang thú cưng lên tàu không?',
          answer:
            'Có, thú cưng cần có đầy đủ giấy tờ tiêm phòng và được đặt trong lồng/túi vận chuyển. Phí: 100,000 VNĐ/con.',
        },
        {
          question: 'Có dịch vụ gửi hành lý không?',
          answer:
            'Có, bạn có thể gửi hành lý tại quầy dịch vụ cảng với phí 20,000 VNĐ/kiện để thoải mái di chuyển trên tàu.',
        },
      ],
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar04Page />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-20">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Trung tâm hỗ trợ</h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Tìm câu trả lời cho các câu hỏi thường gặp về dịch vụ đặt vé tàu
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Contact Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader className="text-center">
              <Phone className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <CardTitle>Hotline 24/7</CardTitle>
              <CardDescription>Hỗ trợ khẩn cấp</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-2xl font-bold text-blue-600 mb-2">1900 1234</p>
              <p className="text-sm text-gray-600">Miễn phí từ điện thoại cố định</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Mail className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <CardTitle>Email hỗ trợ</CardTitle>
              <CardDescription>Phản hồi trong 2 giờ</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-lg font-semibold text-green-600 mb-2"><EMAIL></p>
              <p className="text-sm text-gray-600">Gửi email cho chúng tôi</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <MessageCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <CardTitle>Chat trực tuyến</CardTitle>
              <CardDescription>Phản hồi ngay lập tức</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <StartChatButton />
            </CardContent>
          </Card>
        </div>

        {/* FAQ Sections */}
        <div className="space-y-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Câu hỏi thường gặp</h2>
            <p className="text-lg text-gray-600">
              Tìm câu trả lời nhanh chóng cho các thắc mắc phổ biến
            </p>
          </div>

          {faqCategories.map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-xl">
                  {category.icon}
                  {category.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {category.questions.map((faq, faqIndex) => (
                    <AccordionItem key={faqIndex} value={`${categoryIndex}-${faqIndex}`}>
                      <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                      <AccordionContent className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Help Section */}
        <Card className="mt-12 bg-blue-50 border-blue-200">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-blue-900">Vẫn cần hỗ trợ?</CardTitle>
            <CardDescription className="text-blue-700">
              Đội ngũ hỗ trợ của chúng tôi luôn sẵn sàng giúp đỡ bạn
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <Phone className="h-4 w-4 mr-2" />
                Gọi ngay: 1900 1234
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-50 bg-transparent"
              >
                <Mail className="h-4 w-4 mr-2" />
                Gửi email hỗ trợ
              </Button>
            </div>
            <p className="text-sm text-blue-600 mt-4">
              Thời gian hỗ trợ: 24/7 cho các vấn đề khẩn cấp, 8:00 - 22:00 cho tư vấn chung
            </p>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <div className="mt-12 grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-4 text-center">
                <MapPin className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                <p className="font-medium">Tìm chuyến tàu</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/promotions">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-4 text-center">
                <Ticket className="h-6 w-6 text-green-600 mx-auto mb-2" />
                <p className="font-medium">Khuyến mãi</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/me/bookings">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-4 text-center">
                <Users className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <p className="font-medium">Đơn đặt vé</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/me/profile">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-4 text-center">
                <Users className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <p className="font-medium">Tài khoản</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>

      <Footer />
    </div>
  );
}
