'use client';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { SearchFilters } from '@/components/search/search-filters';
import { TripCard } from '@/components/search/trip-card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FerryService } from '@/lib/ferry-service';
import { getPortById } from '@/lib/mock-data';
import type { SearchParams, TripSearchResult } from '@/lib/types';
import { ArrowLeft, Filter } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
export default function SearchResultsPage() {
  const searchParams = useSearchParams();
  const [results, setResults] = useState<TripSearchResult[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Parse search parameters
  const searchQuery: SearchParams = {
    fromPortId: searchParams.get('from') || '',
    toPortId: searchParams.get('to') || '',
    departureDate: searchParams.get('departure') || '',
    returnDate: searchParams.get('return') || '',
    tripType: (searchParams.get('type') as 'one-way' | 'round-trip') || 'one-way',
    adultCount: Number.parseInt(searchParams.get('adults') || '1'),
    childCount: Number.parseInt(searchParams.get('children') || '0'),
  };

  const fromPort = getPortById(searchQuery.fromPortId);
  const toPort = getPortById(searchQuery.toPortId);

  useEffect(() => {
    const searchResults = FerryService.searchTrips(searchQuery);
    setResults(searchResults);
    setIsLoading(false);
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar04Page />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
        {/* Back Button */}
        <div>
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại tìm kiếm
            </Button>
          </Link>
        </div>

        {/* Search Summary */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl md:text-2xl">
              {' '}
              {fromPort?.name} → {toPort?.name}
            </CardTitle>
            <p className="text-gray-600 text-center">
              {searchQuery.departureDate} • {searchQuery.adultCount} người lớn
              {searchQuery.childCount > 0 && `, ${searchQuery.childCount} trẻ em`} •
              {searchQuery.tripType === 'round-trip' ? ' Khứ hồi' : ' Một chiều'}
            </p>
            <p className="text-sm text-gray-500 mt-1 text-center">
              Tìm thấy {results.length} chuyến
            </p>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="lg:hidden mb-4">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="w-full"
              >
                <Filter className="h-4 w-4 mr-2" />
                Bộ lọc
              </Button>
            </div>

            <div className={`${showFilters ? 'block' : 'hidden'} lg:block`}>
              <SearchFilters />
            </div>
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            {results.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Không tìm thấy chuyến nào
                  </h3>
                  <p className="text-gray-600 mb-4">Thử thay đổi ngày hoặc tuyến đường khác</p>
                  <Link href="/">
                    <Button>Tìm kiếm lại</Button>
                  </Link>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {results.map((result) => (
                  <TripCard key={result.schedule.id} result={result} searchParams={searchQuery} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
