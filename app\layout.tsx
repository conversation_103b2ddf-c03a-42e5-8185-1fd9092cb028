import { Toaster } from '@/components/ui/sonner';
import { ToastProvider } from '@/components/ui/toast-custom';
import { AuthProvider } from '@/lib/auth-context';
import type { Metadata } from 'next';
import { Roboto } from 'next/font/google';
import type React from 'react';
import { Suspense } from 'react';
import './globals.css';

const roboto = Roboto({
  subsets: ['latin'],
  variable: '--font-roboto',
  weight: ['400', '700'],
});

export const metadata: Metadata = {
  title: 'Ocean Pass - Đặt vé tàu cao tốc trực tuyến',
  description: '<PERSON><PERSON> thống đặt vé tàu cao tốc hiện đại, nhanh chóng và tiện lợi',
  icons: {
    icon: '/logo.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`font-sans ${roboto.variable}`}>
        <AuthProvider>
          <ToastProvider>
            <Suspense fallback={null}>{children}</Suspense>
            <Toaster />
          </ToastProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
