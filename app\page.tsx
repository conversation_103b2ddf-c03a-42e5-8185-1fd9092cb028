'use client';

import PromotionsBanner from '@/components/app/home/<USER>';
import CardProduct from '@/components/card/card';
import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { SearchForm } from '@/components/search/search-form';
import { AnimatedText } from '@/components/ui/animated-text';
import { BackgroundLines } from '@/components/ui/background-lines';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { Clock, Shield, Ship, Star } from 'lucide-react';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar04Page />

      {/* Hero Section */}
      <section className="relative text-white py-35">
        <BackgroundLines
          className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-800"
          svgOptions={{ duration: 10 }}
        />
        <div className="absolute inset-0 bg-black/20"></div>
        <motion.div
          className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
          initial={{ opacity: 0, y: 24 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        >
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-balance">
            Đặt vé tàu cao tốc
            <br />
            <span className="text-blue-200 block mt-4">
              <AnimatedText
                words={[
                  'nhanh chóng & tiện lợi',
                  'dễ dàng & an toàn',
                  'hiện đại & đáng tin cậy',
                  'thông minh & tiết kiệm',
                ]}
                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
                interval={3500}
              />
            </span>
          </h1>
          <p className="text-xl sm:text-2xl md:text-3xl mb-8 text-white max-w-4xl mx-auto text-pretty">
            Khám phá các tuyến đường thủy đẹp nhất Việt Nam với hệ thống đặt vé hiện đại
          </p>
        </motion.div>
      </section>

      {/* Search Form Section */}
      <section className="py-12 -mt-35 relative z-10">
        <motion.div
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        >
          <SearchForm />
        </motion.div>
      </section>

      {/* Promotions Banner */}
      <motion.div
        initial={{ opacity: 0, y: 24 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.2 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        <PromotionsBanner />
      </motion.div>

      {/* Features Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.2 }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Tại sao chọn Ocean Pass?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Chúng tôi mang đến trải nghiệm đặt vé tàu thủy tốt nhất với những tính năng vượt trội
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.0 }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Ship className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <CardTitle className="text-xl">Đa dạng tuyến đường</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Kết nối các cảng lớn trên toàn quốc với lịch trình linh hoạt
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.05 }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Clock className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <CardTitle className="text-xl">Đặt vé nhanh chóng</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Chỉ 3 bước đơn giản để hoàn tất đặt vé trong vài phút
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.1 }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Shield className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                  <CardTitle className="text-xl">An toàn & bảo mật</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Thanh toán an toàn với công nghệ mã hóa tiên tiến</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.15 }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <Star className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                  <CardTitle className="text-xl">Hỗ trợ 24/7</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Đội ngũ chăm sóc khách hàng chuyên nghiệp luôn sẵn sàng hỗ trợ
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Popular Routes */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.2 }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Tuyến đường phổ biến
            </h2>
            <p className="text-lg text-gray-600">Những tuyến đường được yêu thích nhất</p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, amount: 0.2 }}
          >
            <motion.div
              variants={{ hidden: { opacity: 0, y: 24 }, show: { opacity: 1, y: 0 } }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.1 }}
            >
              <CardProduct
                image="https://i.imgur.com/4NF4A76.png"
                imageAlt="Ferry to Can Tho"
                title="TP.HCM → Cần Thơ"
                badges={['Phổ biến', '4 giờ']}
                description="Tuyến đường thủy nối liền thành phố Hồ Chí Minh với Cần Thơ, mang đến trải nghiệm du lịch sông nước miền Tây."
                priceLabel="Giá từ"
                price="180,000đ"
                buttonText="Xem thông tin"
                gradientClasses="white"
              />
            </motion.div>
            <motion.div
              variants={{ hidden: { opacity: 0, y: 24 }, show: { opacity: 1, y: 0 } }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.2 }}
            >
              <CardProduct
                image="https://i.imgur.com/EzUukp7.jpeg"
                imageAlt="Ferry to Vung Tau"
                title="TP.HCM → Vũng Tàu"
                badges={['Nhanh nhất', '3 giờ']}
                description="Tuyến đường nhanh chóng đến thành phố biển Vũng Tàu, lý tưởng cho các chuyến du lịch cuối tuần."
                priceLabel="Giá từ"
                price="200,000đ"
                buttonText="Xem thông tin"
                gradientClasses="white"
              />
            </motion.div>
            <motion.div
              variants={{ hidden: { opacity: 0, y: 24 }, show: { opacity: 1, y: 0 } }}
              transition={{ duration: 0.6, ease: 'easeOut', delay: 0.3 }}
            >
              <CardProduct
                image="https://i.imgur.com/GHwauPe.jpeg"
                imageAlt="Ferry to Phu Quoc"
                title="Rạch Giá → Phú Quốc"
                badges={['Du lịch', '2 giờ']}
                description="Tuyến đường nhanh chóng đến hòn đảo Phú Quốc, lý tưởng cho các chuyến du lịch cuối tuần cùng với gia đình."
                priceLabel="Giá từ"
                price="150,000đ"
                buttonText="Xem thông tin"
                gradientClasses="white"
              />
            </motion.div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
