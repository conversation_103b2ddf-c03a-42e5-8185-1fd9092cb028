'use client';

import type React from 'react';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Mock validation - in real app, this would send reset email
    if (!email || !email.includes('@')) {
      setError('Vui lòng nhập địa chỉ email hợp lệ');
      setIsLoading(false);
      return;
    }

    setIsSubmitted(true);
    setIsLoading(false);
  };

  if (isSubmitted) {
    return (
      <div className="flex flex-col min-h-screen bg-gray-50">
        <Navbar04Page />
        <main className="flex-1">
          <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl mx-auto mt-8 mb-8 pt-40 pb-20 px-4">
            <Card className="shadow-lg border-0">
              <CardHeader className="text-center pb-6">
                <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl font-bold">Email đã được gửi</CardTitle>
                <CardDescription className="text-lg">
                  Chúng tôi đã gửi hướng dẫn đặt lại mật khẩu đến email của bạn
                </CardDescription>
              </CardHeader>
              <CardContent className="px-6 pb-6 space-y-5">
                <div className="text-center p-4 bg-gray-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">Kiểm tra hộp thư đến của bạn tại:</p>
                  <p className="font-mono font-medium text-gray-900 bg-white px-3 py-2 rounded border">
                    {email}
                  </p>
                </div>

                <div className="text-center p-4 bg-blue-50 rounded-xl">
                  <p className="text-sm text-blue-700 mb-2">Không nhận được email?</p>
                  <p className="text-sm text-blue-600 mb-3">Kiểm tra thư mục spam hoặc</p>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-blue-600 font-medium"
                    onClick={() => {
                      setIsSubmitted(false);
                      setEmail('');
                    }}
                  >
                    thử lại với email khác
                  </Button>
                </div>

                <div className="pt-2">
                  <Link href="/auth/login">
                    <Button
                      variant="outline"
                      className="w-full h-12 text-lg font-medium bg-transparent"
                    >
                      <ArrowLeft className="h-5 w-5 mr-2" />
                      Quay lại đăng nhập
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />
      <main className="flex-1">
        <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl mx-auto mt-8 mb-8 pt-40 pb-20 px-4">
          <Card className="shadow-lg border-0">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-bold">Quên mật khẩu</CardTitle>
              <CardDescription className="text-lg">
                Nhập địa chỉ email của bạn và chúng tôi sẽ gửi hướng dẫn đặt lại mật khẩu
              </CardDescription>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <form onSubmit={handleSubmit} className="space-y-5">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="h-11"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full h-10 text-base font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? 'Đang gửi...' : 'Gửi hướng dẫn đặt lại'}
                </Button>
              </form>

              <div className="mt-8 text-center">
                <Link
                  href="/auth/login"
                  className="inline-flex items-center text-blue-600 hover:underline font-medium"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Quay lại đăng nhập
                </Link>
              </div>

              <div className="mt-6 text-center">
                <p className="text-gray-600">
                  Chưa có tài khoản?{' '}
                  <Link href="/auth/register" className="text-blue-600 hover:underline font-medium">
                    Đăng ký ngay
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}
