'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { getBookingCountByUser, getTotalSpentByUser, mockUsers } from '@/lib/mock-data';
import type { User } from '@/lib/types';
import { Calendar, Eye, Mail, Phone, Search } from 'lucide-react';
import { useState } from 'react';

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<User | null>(null);

  const filteredCustomers = mockUsers.filter((customer) => {
    const term = searchTerm.toLowerCase();
    return (
      customer.name.toLowerCase().includes(term) ||
      customer.email.toLowerCase().includes(term) ||
      (customer.phone ?? '').toLowerCase().includes(term)
    );
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const calculateAge = (dateOfBirth?: string): number | string => {
    if (!dateOfBirth) return '-';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const getCustomerStatus = (userId: string) => {
    const bookingCount = getBookingCountByUser(userId);
    if (bookingCount === 0) return { label: 'Mới', variant: 'secondary' as const };
    if (bookingCount >= 5) return { label: 'VIP', variant: 'default' as const };
    return { label: 'Thường xuyên', variant: 'outline' as const };
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Quản lý khách hàng</h1>
        <p className="text-gray-600 mt-2">Danh sách và thông tin chi tiết khách hàng</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng khách hàng</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockUsers.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khách hàng mới (tháng này)</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                mockUsers.filter(
                  (user) => new Date(user.createdAt).getMonth() === new Date().getMonth()
                ).length
              }
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khách hàng VIP</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockUsers.filter((user) => getBookingCountByUser(user.id) >= 5).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng doanh thu</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                mockUsers.reduce((total, user) => total + getTotalSpentByUser(user.id), 0)
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách khách hàng</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm kiếm theo tên, email hoặc số điện thoại..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Liên hệ</TableHead>
                  <TableHead>Tuổi</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Số đơn hàng</TableHead>
                  <TableHead>Tổng chi tiêu</TableHead>
                  <TableHead>Ngày đăng ký</TableHead>
                  <TableHead>Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => {
                  const bookingCount = getBookingCountByUser(customer.id);
                  const totalSpent = getTotalSpentByUser(customer.id);
                  const status = getCustomerStatus(customer.id);

                  return (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{customer.name}</div>
                          <div className="text-sm text-gray-500">{customer.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Mail className="h-3 w-3 mr-1" />
                            {customer.email}
                          </div>
                          <div className="flex items-center text-sm">
                            <Phone className="h-3 w-3 mr-1" />
                            {customer.phone ?? ''}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{calculateAge(customer.dateOfBirth)} tuổi</TableCell>
                      <TableCell>
                        <Badge variant={status.variant}>{status.label}</Badge>
                      </TableCell>
                      <TableCell>{bookingCount}</TableCell>
                      <TableCell>{formatCurrency(totalSpent)}</TableCell>
                      <TableCell>{formatDate(customer.createdAt)}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedCustomer(customer)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Xem
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Chi tiết khách hàng</DialogTitle>
                            </DialogHeader>
                            {selectedCustomer && (
                              <div className="space-y-6">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">
                                      Họ tên
                                    </label>
                                    <p className="text-lg font-medium">{selectedCustomer.name}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">
                                      Email
                                    </label>
                                    <p>{selectedCustomer.email}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">
                                      Số điện thoại
                                    </label>
                                    <p>{selectedCustomer.phone}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">
                                      Ngày sinh
                                    </label>
                                    <p>
                                      {formatDate(selectedCustomer.dateOfBirth)} (
                                      {calculateAge(selectedCustomer.dateOfBirth)} tuổi)
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">
                                      Ngày đăng ký
                                    </label>
                                    <p>{formatDate(selectedCustomer.createdAt)}</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-500">
                                      Trạng thái
                                    </label>
                                    <div className="mt-1">
                                      <Badge
                                        variant={getCustomerStatus(selectedCustomer.id).variant}
                                      >
                                        {getCustomerStatus(selectedCustomer.id).label}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>

                                <div className="border-t pt-4">
                                  <h3 className="text-lg font-medium mb-4">Thống kê đặt vé</h3>
                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="bg-blue-50 p-4 rounded-lg">
                                      <div className="text-2xl font-bold text-blue-600">
                                        {getBookingCountByUser(selectedCustomer.id)}
                                      </div>
                                      <div className="text-sm text-blue-600">Tổng số đơn hàng</div>
                                    </div>
                                    <div className="bg-green-50 p-4 rounded-lg">
                                      <div className="text-2xl font-bold text-green-600">
                                        {formatCurrency(getTotalSpentByUser(selectedCustomer.id))}
                                      </div>
                                      <div className="text-sm text-green-600">Tổng chi tiêu</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
