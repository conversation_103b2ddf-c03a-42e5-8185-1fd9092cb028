// QR Code generation utilities
export function generateQRCode(ticketId: string, bookingCode: string): string {
  // In a real app, this would generate an actual QR code
  // For demo purposes, we'll create a data URL with ticket info
  const qrData = `FERRY-TICKET:${ticketId}:${bookingCode}`

  // This is a placeholder - in production you'd use a QR code library
  // like 'qrcode' to generate actual QR codes
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="white"/>
      <rect x="20" y="20" width="160" height="160" fill="none" stroke="black" stroke-width="2"/>
      <text x="100" y="100" text-anchor="middle" font-family="monospace" font-size="12" fill="black">
        ${qrData}
      </text>
      <text x="100" y="120" text-anchor="middle" font-family="sans-serif" font-size="8" fill="gray">
        QR Code
      </text>
    </svg>
  `)}`
}

export function generateTicketPDF(
  ticket: any,
  booking: any,
  schedule: any,
  route: any,
  vessel: any,
  fromPort: any,
  toPort: any,
): string {
  // In a real app, this would generate a PDF using libraries like jsPDF
  // For demo purposes, we'll return a data URL
  return "data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVGl0bGUgKEZlcnJ5IFRpY2tldCkKL0NyZWF0b3IgKEZlcnJ5IEJvb2tpbmcgU3lzdGVtKQovUHJvZHVjZXIgKEZlcnJ5IEJvb2tpbmcgU3lzdGVtKQovQ3JlYXRpb25EYXRlIChEOjIwMjQwMTAxMTIwMDAwKQo+PgplbmRvYmoKCjIgMCBvYmoKPDwKL1R5cGUgL0NhdGFsb2cKL1BhZ2VzIDMgMCBSCj4+CmVuZG9iagoKMyAwIG9iago8PAovVHlwZSAvUGFnZXMKL0tpZHMgWzQgMCBSXQovQ291bnQgMQo+PgplbmRvYmoKCjQgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAzIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQovQ29udGVudHMgNSAwIFIKPj4KZW5kb2JqCgo1IDAgb2JqCjw8Ci9MZW5ndGggNDQKPj4Kc3RyZWFtCkJUCi9GMSAxMiBUZgoxMDAgNzAwIFRkCihGZXJyeSBUaWNrZXQpIFRqCkVUCmVuZHN0cmVhbQplbmRvYmoKCnhyZWYKMCA2CjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAwMDAwOSAwMDAwMCBuIAowMDAwMDAwMTc0IDAwMDAwIG4gCjAwMDAwMDAyMjEgMDAwMDAgbiAKMDAwMDAwMDI3OCAwMDAwMCBuIAowMDAwMDAwMzc4IDAwMDAwIG4gCnRyYWlsZXIKPDwKL1NpemUgNgovUm9vdCAyIDAgUgo+PgpzdGFydHhyZWYKNDcyCiUlRU9G"
}
