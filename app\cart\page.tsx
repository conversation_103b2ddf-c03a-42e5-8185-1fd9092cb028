'use client';

import { Footer } from '@/components/layout/footer';
import { ShinyButton } from '@/components/magicui/shiny-button';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { FerryService } from '@/lib/ferry-service';
import {
  getPortById,
  getRouteById,
  getScheduleById,
  getSeatMapById,
  getVesselById,
} from '@/lib/mock-data';
import type { Port, Promotion, Route, Schedule, Vessel } from '@/lib/types';
import { ArrowLeft, Clock, MapPin, Tag, Trash2, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

interface CartItem {
  seatId: string;
  seatNumber: string;
  passengerType: 'adult' | 'child';
  price: number;
}

export default function CartPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [route, setRoute] = useState<Route | null>(null);
  const [vessel, setVessel] = useState<Vessel | null>(null);
  const [fromPort, setFromPort] = useState<Port | null>(null);
  const [toPort, setToPort] = useState<Port | null>(null);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState<Promotion | null>(null);
  const [promoError, setPromoError] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Parse URL parameters
  const scheduleId = searchParams.get('scheduleId') || '';
  const seatsParam = searchParams.get('seats') || '';
  const searchQuery = {
    fromPortId: searchParams.get('from') || '',
    toPortId: searchParams.get('to') || '',
    departureDate: searchParams.get('departure') || '',
    returnDate: searchParams.get('return') || '',
    tripType: (searchParams.get('type') as 'one-way' | 'round-trip') || 'one-way',
    adultCount: Number.parseInt(searchParams.get('adults') || '1'),
    childCount: Number.parseInt(searchParams.get('children') || '0'),
  };

  useEffect(() => {
    const scheduleData = getScheduleById(scheduleId);
    if (scheduleData) {
      const routeData = getRouteById(scheduleData.routeId);
      const vesselData = getVesselById(scheduleData.vesselId);
      const fromPortData = getPortById(routeData?.fromPortId || '');
      const toPortData = getPortById(routeData?.toPortId || '');

      setSchedule(scheduleData);
      setRoute(routeData || null);
      setVessel(vesselData || null);
      setFromPort(fromPortData || null);
      setToPort(toPortData || null);

      // Parse selected seats
      if (seatsParam && vesselData) {
        const seatMap = getSeatMapById(vesselData.seatMapId);
        if (seatMap) {
          const items: CartItem[] = [];
          const seatPairs = seatsParam.split(',');

          seatPairs.forEach((pair) => {
            const [seatId, passengerType] = pair.split(':');

            // Find seat in seat map
            let seatFound = false;
            seatMap.decks.forEach((deck) => {
              deck.sections.forEach((section) => {
                section.rows.forEach((row) => {
                  const seat = row.seats.find((s) => s.id === seatId);
                  if (seat && !seatFound) {
                    items.push({
                      seatId: seat.id,
                      seatNumber: seat.number,
                      passengerType: passengerType as 'adult' | 'child',
                      price: seat.price[passengerType as 'adult' | 'child'],
                    });
                    seatFound = true;
                  }
                });
              });
            });
          });

          setCartItems(items);
        }
      }
    }
    setIsLoading(false);
  }, [scheduleId, seatsParam]);

  const subtotal = cartItems.reduce((sum, item) => sum + item.price, 0);
  const discountAmount = appliedPromo ? calculateDiscount(subtotal, appliedPromo) : 0;
  const total = subtotal - discountAmount;

  function calculateDiscount(amount: number, promo: Promotion): number {
    if (promo.minAmount && amount < promo.minAmount) return 0;

    if (promo.type === 'percentage') {
      const discount = (amount * promo.value) / 100;
      return promo.maxDiscount ? Math.min(discount, promo.maxDiscount) : discount;
    } else {
      return promo.value;
    }
  }

  const handleApplyPromo = async () => {
    try {
      setPromoError('');
      if (!promoCode) return;
      const res = await fetch(`/api/promotions?code=${encodeURIComponent(promoCode)}`, {
        cache: 'no-store',
      });
      const data = await res.json().catch(() => ({}));
      if (!res.ok) {
        setPromoError((data && (data.message || data.error)) || 'Mã khuyến mãi không hợp lệ');
        return;
      }
      const items = (data && (data.data?.items || data.items || data.data || data)) || [];
      const item = Array.isArray(items) ? items.find((i: any) => i.code === promoCode) : null;
      if (!item) {
        setPromoError('Mã khuyến mãi không hợp lệ');
        return;
      }
      const promotion: Promotion = {
        id: String(item.id ?? ''),
        code: item.code ?? promoCode,
        name: item.name ?? promoCode,
        description: item.description ?? '',
        type: (item.type === 'percentage' ? 'percentage' : 'fixed') as Promotion['type'],
        value: Number(item.value ?? 0),
        minAmount: item.min_amount != null ? Number(item.min_amount) : undefined,
        maxDiscount: item.max_discount != null ? Number(item.max_discount) : undefined,
        validFrom: item.valid_from ?? '',
        validTo: item.valid_to ?? '',
        isActive: Boolean(item.is_active ?? true),
      };

      if (promotion.minAmount && subtotal < promotion.minAmount) {
        setPromoError(`Đơn hàng tối thiểu ${FerryService.formatCurrency(promotion.minAmount)}`);
        return;
      }

      setAppliedPromo(promotion);
      setPromoCode('');
    } catch (e) {
      setPromoError('Không thể kiểm tra mã khuyến mãi.');
    }
  };

  const handleRemovePromo = () => {
    setAppliedPromo(null);
    setPromoError('');
  };

  const handleProceedToCheckout = () => {
    const checkoutParams = new URLSearchParams({
      scheduleId,
      seats: seatsParam,
      from: searchQuery.fromPortId,
      to: searchQuery.toPortId,
      departure: searchQuery.departureDate,
      adults: searchQuery.adultCount.toString(),
      children: searchQuery.childCount.toString(),
      type: searchQuery.tripType,
      subtotal: subtotal.toString(),
      discount: discountAmount.toString(),
      total: total.toString(),
      ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
      ...(appliedPromo && { promoCode: appliedPromo.code }),
    });

    router.push(`/checkout/passengers?${checkoutParams.toString()}`);
  };

  const backUrl = `/trip/${scheduleId}?${new URLSearchParams({
    from: searchQuery.fromPortId,
    to: searchQuery.toPortId,
    departure: searchQuery.departureDate,
    adults: searchQuery.adultCount.toString(),
    children: searchQuery.childCount.toString(),
    type: searchQuery.tripType,
    ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
  }).toString()}`;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!schedule || !route || !vessel || !fromPort || !toPort || cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Giỏ hàng trống</h1>
            <p className="text-gray-600 mb-6">Không có vé nào trong giỏ hàng của bạn</p>
            <Link href="/">
              <Button>Quay lại trang chủ</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />

      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          {/* Back Button */}
          <Link href={backUrl}>
            <Button variant="ghost" className="mb-6">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại chọn ghế
            </Button>
          </Link>

          {/* Page Title */}
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Giỏ hàng</h1>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-6">
              {/* Trip Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>Thông tin chuyến</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-lg">
                          {fromPort.name} → {toPort.name}
                        </p>
                        <p className="text-gray-600">
                          {vessel.name} ({vessel.code})
                        </p>
                      </div>
                      <Badge variant="secondary">
                        {searchQuery.tripType === 'round-trip' ? 'Khứ hồi' : 'Một chiều'}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span>
                          {searchQuery.departureDate} • {schedule.departureTime}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span>
                          {searchQuery.adultCount} người lớn
                          {searchQuery.childCount > 0 && `, ${searchQuery.childCount} trẻ em`}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Selected Seats */}
              <Card>
                <CardHeader>
                  <CardTitle>Ghế đã chọn</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {cartItems.map((item) => (
                      <div
                        key={`${item.seatId}-${item.passengerType}`}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span className="text-sm font-semibold text-blue-600">
                              {item.seatNumber}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">Ghế {item.seatNumber}</p>
                            <p className="text-sm text-gray-600">
                              {item.passengerType === 'adult' ? 'Người lớn' : 'Trẻ em'}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{FerryService.formatCurrency(item.price)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle>Tóm tắt đơn hàng</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Promo Code */}
                  <div>
                    <Label htmlFor="promo-code">Mã khuyến mãi</Label>
                    <div className="flex space-x-2 mt-2">
                      <Input
                        id="promo-code"
                        placeholder="Nhập mã khuyến mãi"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                        disabled={!!appliedPromo}
                      />
                      {appliedPromo ? (
                        <Button variant="outline" onClick={handleRemovePromo}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      ) : (
                        <Button variant="outline" onClick={handleApplyPromo} disabled={!promoCode}>
                          <Tag className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    {promoError && <p className="text-sm text-red-600 mt-1">{promoError}</p>}
                    {appliedPromo && (
                      <div className="mt-2 p-2 bg-green-50 rounded-lg">
                        <p className="text-sm text-green-800 font-medium">{appliedPromo.name}</p>
                        <p className="text-xs text-green-600">{appliedPromo.description}</p>
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Price Breakdown */}
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Tạm tính</span>
                      <span>{FerryService.formatCurrency(subtotal)}</span>
                    </div>
                    {appliedPromo && (
                      <div className="flex justify-between text-green-600">
                        <span>Giảm giá ({appliedPromo.code})</span>
                        <span>-{FerryService.formatCurrency(discountAmount)}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Tổng cộng</span>
                      <span className="text-blue-600">{FerryService.formatCurrency(total)}</span>
                    </div>
                  </div>

                  {/* Checkout Button */}
                  <ShinyButton
                    onClick={handleProceedToCheckout}
                    className="w-full font-medium bg-blue-600 hover:bg-blue-700"
                  >
                    <span className="flex items-center justify-center text-white py-1">
                      Tiếp tục thanh toán
                    </span>
                  </ShinyButton>

                  <p className="text-xs text-gray-500 text-center">Ghế sẽ được giữ trong 15 phút</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
