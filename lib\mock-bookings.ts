import type { Booking, Ticket } from "./types"
import { generateQRCode } from "./qr-utils"

// Mock bookings data
export const mockBookings: Booking[] = [
  {
    id: "booking-1",
    code: "FB240101001",
    userId: "2",
    tripType: "one-way",
    outboundScheduleId: "schedule-1",
    passengers: [
      {
        id: "passenger-1",
        name: "Nguyễn Văn A",
        age: 30,
        type: "adult",
        seatId: "seat-a1",
      },
      {
        id: "passenger-2",
        name: "<PERSON><PERSON><PERSON><PERSON>ị B",
        age: 8,
        type: "child",
        seatId: "seat-a2",
      },
    ],
    totalAmount: 450000,
    discountAmount: 0,
    status: "confirmed",
    paymentMethod: "card",
    createdAt: "2024-01-01T10:00:00Z",
    updatedAt: "2024-01-01T10:05:00Z",
  },
  {
    id: "booking-2",
    code: "FB240102002",
    userId: "2",
    tripType: "round-trip",
    outboundScheduleId: "schedule-2",
    returnScheduleId: "schedule-3",
    passengers: [
      {
        id: "passenger-3",
        name: "<PERSON><PERSON><PERSON><PERSON>",
        age: 30,
        type: "adult",
        seatId: "seat-b1",
      },
    ],
    totalAmount: 600000,
    discountAmount: 50000,
    promotionCode: "NEWYEAR2024",
    status: "confirmed",
    paymentMethod: "e-wallet",
    createdAt: "2024-01-02T14:30:00Z",
    updatedAt: "2024-01-02T14:35:00Z",
  },
]

// Generate tickets from bookings
export const mockTickets: Ticket[] = mockBookings.flatMap((booking) => {
  const tickets: Ticket[] = []

  // Outbound tickets
  booking.passengers.forEach((passenger) => {
    const ticketId = `ticket-${booking.id}-${passenger.id}-outbound`
    tickets.push({
      id: ticketId,
      bookingId: booking.id,
      scheduleId: booking.outboundScheduleId,
      passenger,
      qrCode: generateQRCode(ticketId, booking.code),
      isUsed: false,
    })
  })

  // Return tickets (if round-trip)
  if (booking.returnScheduleId) {
    booking.passengers.forEach((passenger) => {
      const ticketId = `ticket-${booking.id}-${passenger.id}-return`
      tickets.push({
        id: ticketId,
        bookingId: booking.id,
        scheduleId: booking.returnScheduleId!,
        passenger,
        qrCode: generateQRCode(ticketId, booking.code),
        isUsed: false,
      })
    })
  }

  return tickets
})

export function getBookingsByUserId(userId: string): Booking[] {
  return mockBookings.filter((booking) => booking.userId === userId)
}

export function getTicketsByBookingId(bookingId: string): Ticket[] {
  return mockTickets.filter((ticket) => ticket.bookingId === bookingId)
}

export function getBookingByCode(code: string): Booking | undefined {
  return mockBookings.find((booking) => booking.code === code)
}
