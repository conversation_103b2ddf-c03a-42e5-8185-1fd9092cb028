"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Search, Edit, Calendar, Clock, DollarSign, Ship } from "lucide-react"
import { mockSchedules, mockRoutes, mockVessels, getRouteById, getVesselById, getPortById } from "@/lib/mock-data"

export default function SchedulesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const filteredSchedules = mockSchedules.filter((schedule) => {
    const route = getRouteById(schedule.routeId)
    const vessel = getVesselById(schedule.vesselId)
    const fromPort = route ? getPortById(route.fromPortId) : null
    const toPort = route ? getPortById(route.toPortId) : null

    const matchesSearch =
      vessel?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fromPort?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      toPort?.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || schedule.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount)
  }

  const formatTime = (time: string) => {
    return time
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN")
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Hoạt động</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">Đã hủy</Badge>
      case "completed":
        return <Badge className="bg-gray-100 text-gray-800">Hoàn thành</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quản lý Lịch trình</h1>
          <p className="text-gray-600">Quản lý lịch trình chuyến đi và giá vé</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Thêm lịch trình
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Thêm lịch trình mới</DialogTitle>
              <DialogDescription>Tạo lịch trình chuyến đi mới</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="route">Tuyến đường</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn tuyến đường" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockRoutes.map((route) => {
                      const fromPort = getPortById(route.fromPortId)
                      const toPort = getPortById(route.toPortId)
                      return (
                        <SelectItem key={route.id} value={route.id}>
                          {fromPort?.name} → {toPort?.name}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="vessel">Tàu</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn tàu" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockVessels.map((vessel) => (
                      <SelectItem key={vessel.id} value={vessel.id}>
                        {vessel.name} ({vessel.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="departure-time">Giờ khởi hành</Label>
                  <Input id="departure-time" type="time" />
                </div>
                <div>
                  <Label htmlFor="arrival-time">Giờ đến</Label>
                  <Input id="arrival-time" type="time" />
                </div>
              </div>
              <div>
                <Label htmlFor="date">Ngày</Label>
                <Input id="date" type="date" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="adult-price">Giá người lớn</Label>
                  <Input id="adult-price" type="number" placeholder="200000" />
                </div>
                <div>
                  <Label htmlFor="child-price">Giá trẻ em</Label>
                  <Input id="child-price" type="number" placeholder="140000" />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={() => setIsAddDialogOpen(false)}>Thêm lịch trình</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng lịch trình</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockSchedules.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đang hoạt động</CardTitle>
            <Calendar className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockSchedules.filter((s) => s.status === "active").length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Giá TB người lớn</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(mockSchedules.reduce((total, s) => total + s.basePrice.adult, 0) / mockSchedules.length)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tuyến phổ biến</CardTitle>
            <Ship className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">HCM-CTH</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách lịch trình</CardTitle>
          <CardDescription>Quản lý lịch trình các chuyến đi</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm theo tàu, cảng..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="cancelled">Đã hủy</SelectItem>
                <SelectItem value="completed">Hoàn thành</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tuyến đường</TableHead>
                <TableHead>Tàu</TableHead>
                <TableHead>Ngày</TableHead>
                <TableHead>Giờ khởi hành</TableHead>
                <TableHead>Giờ đến</TableHead>
                <TableHead>Giá vé</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSchedules.map((schedule) => {
                const route = getRouteById(schedule.routeId)
                const vessel = getVesselById(schedule.vesselId)
                const fromPort = route ? getPortById(route.fromPortId) : null
                const toPort = route ? getPortById(route.toPortId) : null

                return (
                  <TableRow key={schedule.id}>
                    <TableCell className="font-medium">
                      {fromPort?.name} → {toPort?.name}
                    </TableCell>
                    <TableCell>{vessel?.name}</TableCell>
                    <TableCell>{formatDate(schedule.date)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                        {formatTime(schedule.departureTime)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                        {formatTime(schedule.arrivalTime)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>NL: {formatCurrency(schedule.basePrice.adult)}</div>
                        <div className="text-muted-foreground">TE: {formatCurrency(schedule.basePrice.child)}</div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(schedule.status)}</TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
