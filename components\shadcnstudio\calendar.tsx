'use client';

import { Calendar } from '@/components/ui/calendar';
import { useState } from 'react';

export function CalendarWithMonthYearDropdown(props: any) {
  const defaultFromMonth = new Date(2025, 0, 1);
  const defaultToMonth = new Date(2030, 11, 31);

  const clampDateToRange = (d?: Date) => {
    if (!d) return undefined;
    if (d < defaultFromMonth) return defaultFromMonth;
    if (d > defaultToMonth) return defaultToMonth;
    return d;
  };

  // Internal selected date state: only initialize from props.selected (do not default to today)
  const [date, setDate] = useState<Date | undefined>(clampDateToRange(props.selected ?? undefined));

  return (
    <div>
      <Calendar
        {...props}
        mode={props.mode ?? 'single'}
        selected={props.selected ?? date}
        onSelect={(d: any) => {
          const clamped = clampDateToRange(d as Date | undefined);
          if (props.onSelect) props.onSelect(clamped);
          setDate(clamped as Date | undefined);
        }}
        className={props.className ?? 'rounded-md border'}
        captionLayout={props.captionLayout ?? 'dropdown'}
        fromMonth={props.fromMonth ?? defaultFromMonth}
        toMonth={props.toMonth ?? defaultToMonth}
      />
    </div>
  );
}

export default CalendarWithMonthYearDropdown;
