'use client';

import { <PERSON>yButton } from '@/components/magicui/shiny-button';
import { CalendarWithMonthYearDropdown } from '@/components/shadcnstudio/calendar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { showWarningToast } from '@/components/ui/soft-toasts';
import { mockPorts } from '@/lib/mock-data';
import type { SearchParams, TripType } from '@/lib/types';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import { ArrowLeftRight, ArrowRight, Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export function SearchForm() {
  const router = useRouter();
  const [searchParams, setSearchParams] = useState<SearchParams>({
    fromPortId: '',
    toPortId: '',
    departureDate: '',
    returnDate: '',
    tripType: 'one-way',
    adultCount: 1,
    childCount: 0,
  });

  const [departureCalendarOpen, setDepartureCalendarOpen] = useState(false);
  const [returnCalendarOpen, setReturnCalendarOpen] = useState(false);

  const handleSearch = () => {
    if (!searchParams.fromPortId || !searchParams.toPortId || !searchParams.departureDate) {
      showWarningToast('Vui lòng chọn đầy đủ cảng đi, cảng đến và ngày đi.');
      return;
    }

    const queryParams = new URLSearchParams({
      from: searchParams.fromPortId,
      to: searchParams.toPortId,
      departure: searchParams.departureDate,
      adults: searchParams.adultCount.toString(),
      children: searchParams.childCount.toString(),
      type: searchParams.tripType,
    });

    if (searchParams.tripType === 'round-trip') {
      if (!searchParams.returnDate) {
        showWarningToast('Vui lòng chọn ngày về cho chuyến khứ hồi.');
        return;
      }
      queryParams.set('return', searchParams.returnDate);
    }

    router.push(`/search/results?${queryParams.toString()}`);
  };

  const swapPorts = () => {
    setSearchParams((prev) => ({
      ...prev,
      fromPortId: prev.toPortId,
      toPortId: prev.fromPortId,
    }));
  };

  const tripOptions = [
    { value: 'one-way', label: 'Một chiều', icon: ArrowRight },
    { value: 'round-trip', label: 'Khứ hồi', icon: ArrowLeftRight },
  ];

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-6">
        {/* Trip Type Selection */}
        <RadioGroupPrimitive.Root
          value={searchParams.tripType}
          onValueChange={(value: TripType) =>
            setSearchParams((prev) => ({ ...prev, tripType: value }))
          }
          className="w-full grid grid-cols-2 gap-4 mb-6"
        >
          {tripOptions.map((option) => (
            <RadioGroupPrimitive.Item
              key={option.value}
              value={option.value}
              className="ring-[1px] ring-border rounded py-2 px-4 data-[state=checked]:ring-2 data-[state=checked]:ring-blue-500 data-[state=checked]:bg-[hsl(214.3_94.6%_92.7%)] text-center"
            >
              <span className="font-semibold tracking-tight flex items-center justify-center gap-4">
                {option.label}
                {option.icon && <option.icon className="h-4 w-4" />}
              </span>
            </RadioGroupPrimitive.Item>
          ))}
        </RadioGroupPrimitive.Root>

        {/* Route and Travel Information */}
        <div className="space-y-6 mb-6">
          {/* Ports Selection */}
          <div className="bg-gray-50/50 p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Tuyến đường</h3>
            <div className="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-5 lg:gap-4 lg:items-end">
              {/* From Port */}
              <div className="w-full lg:col-span-2 space-y-2">
                <Label htmlFor="from-port" className="text-sm font-medium">
                  Cảng đi
                </Label>
                <Select
                  value={searchParams.fromPortId}
                  onValueChange={(value) =>
                    setSearchParams((prev) => ({ ...prev, fromPortId: value }))
                  }
                >
                  <SelectTrigger className="flex h-11 w-full items-center justify-center text-center">
                    <SelectValue placeholder="Chọn cảng đi" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockPorts.map((port) => (
                      <SelectItem key={port.id} value={port.id} className="flex justify-center">
                        <span className="font-medium">
                          {port.name} -{' '}
                          <span className="text-xs text-muted-foreground">{port.code}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Swap Button */}
              <div className="flex justify-center lg:justify-center">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={swapPorts}
                  className="h-11 w-11 bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-colors"
                  title="Đổi chiều cảng"
                >
                  <ArrowLeftRight className="h-4 w-4 text-blue-600" />
                </Button>
              </div>

              {/* To Port */}
              <div className="w-full lg:col-span-2 space-y-2">
                <Label htmlFor="to-port" className="text-sm font-medium">
                  Cảng đến
                </Label>
                <Select
                  value={searchParams.toPortId}
                  onValueChange={(value) =>
                    setSearchParams((prev) => ({ ...prev, toPortId: value }))
                  }
                >
                  <SelectTrigger className="flex h-11 w-full items-center justify-center text-center">
                    <SelectValue placeholder="Chọn cảng đến" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockPorts.map((port) => (
                      <SelectItem key={port.id} value={port.id} className="flex justify-center">
                        <span className="font-medium">
                          {port.name} -{' '}
                          <span className="text-xs text-muted-foreground">{port.code}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Dates Selection */}
          <div className="bg-gray-50/50 p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Thời gian</h3>
            <div
              className={`grid gap-4 ${
                searchParams.tripType === 'round-trip'
                  ? 'grid-cols-1 md:grid-cols-2'
                  : 'grid-cols-1 md:grid-cols-1'
              }`}
            >
              {/* Departure Date */}
              <div className="space-y-2">
                <Label htmlFor="departure-date" className="text-sm font-medium">
                  Ngày đi
                </Label>
                <Popover open={departureCalendarOpen} onOpenChange={setDepartureCalendarOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full h-11 justify-start text-left font-normal hover:bg-white"
                    >
                      {searchParams.departureDate
                        ? new Date(searchParams.departureDate).toLocaleDateString('vi-VN', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                          })
                        : 'Chọn ngày đi'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarWithMonthYearDropdown
                      mode="single"
                      selected={
                        searchParams.departureDate
                          ? new Date(searchParams.departureDate)
                          : undefined
                      }
                      onSelect={(date: Date | undefined) => {
                        setSearchParams((prev) => ({
                          ...prev,
                          departureDate: date
                            ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
                                2,
                                '0'
                              )}-${String(date.getDate()).padStart(2, '0')}`
                            : '',
                        }));
                        setDepartureCalendarOpen(false);
                      }}
                      disabled={(date: Date) => {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Return Date (for round-trip) */}
              {searchParams.tripType === 'round-trip' && (
                <div className="space-y-2">
                  <Label htmlFor="return-date" className="text-sm font-medium">
                    Ngày về
                  </Label>
                  <Popover open={returnCalendarOpen} onOpenChange={setReturnCalendarOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-11 justify-start text-left font-normal hover:bg-white"
                      >
                        {searchParams.returnDate
                          ? new Date(searchParams.returnDate).toLocaleDateString('vi-VN', {
                              weekday: 'short',
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            })
                          : 'Chọn ngày về'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarWithMonthYearDropdown
                        mode="single"
                        selected={
                          searchParams.returnDate ? new Date(searchParams.returnDate) : undefined
                        }
                        onSelect={(date: Date | undefined) => {
                          setSearchParams((prev) => ({
                            ...prev,
                            returnDate: date
                              ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
                                  2,
                                  '0'
                                )}-${String(date.getDate()).padStart(2, '0')}`
                              : '',
                          }));
                          setReturnCalendarOpen(false);
                        }}
                        disabled={(date: Date) => {
                          const departureDate = searchParams.departureDate
                            ? new Date(searchParams.departureDate)
                            : new Date();
                          departureDate.setHours(0, 0, 0, 0);
                          return date < departureDate;
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Passenger Information */}
        <div className="bg-gray-50/50 p-4 rounded-lg border mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Hành khách</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="adult-count" className="text-sm font-medium">
                Người lớn
              </Label>
              <Select
                value={searchParams.adultCount.toString()}
                onValueChange={(value) =>
                  setSearchParams((prev) => ({ ...prev, adultCount: Number.parseInt(value) }))
                }
              >
                <SelectTrigger className="h-11">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((count) => (
                    <SelectItem key={count} value={count.toString()}>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{count}</span>
                        <span className="text-sm text-muted-foreground">người lớn</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="child-count" className="text-sm font-medium">
                Trẻ em
                <span className="text-xs text-muted-foreground ml-1">(2-12 tuổi)</span>
              </Label>
              <Select
                value={searchParams.childCount.toString()}
                onValueChange={(value) =>
                  setSearchParams((prev) => ({ ...prev, childCount: Number.parseInt(value) }))
                }
              >
                <SelectTrigger className="h-11">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[0, 1, 2, 3, 4, 5, 6, 7, 8].map((count) => (
                    <SelectItem key={count} value={count.toString()}>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{count}</span>
                        <span className="text-sm text-muted-foreground">
                          {count === 0 ? 'không có' : count === 1 ? 'trẻ em' : 'trẻ em'}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Search Button */}
        <div className="pt-2">
          <ShinyButton
            onClick={handleSearch}
            className="w-full text-white font-medium bg-blue-600 hover:bg-blue-700"
          >
            <span className="flex items-center justify-center text-white py-1">
              <Search className="h-5 w-5 mr-3" />
              Tìm chuyến phù hợp
            </span>
          </ShinyButton>
        </div>
      </CardContent>
    </Card>
  );
}
