'use client';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/lib/auth-context';
import { getTicketsByBookingId, mockBookings } from '@/lib/mock-bookings';
import { mockPorts, mockRoutes, mockSchedules, mockVessels } from '@/lib/mock-data';
import { generateTicketPDF } from '@/lib/qr-utils';
import { ArrowLeft, Calendar, Download, MapPin, Ship, User } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { use } from 'react';

export default function BookingDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth();
  const { id: bookingId } = use(params);

  if (!user) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Navbar04Page />

        <main className="flex-1">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
            <Alert>
              <AlertDescription>Vui lòng đăng nhập để xem đơn đặt vé.</AlertDescription>
            </Alert>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  const booking = mockBookings.find((b) => b.id === bookingId && b.userId === user.id);

  if (!booking) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Navbar04Page />
        <main className="flex-1">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
            <Alert variant="destructive">
              <AlertDescription>Không tìm thấy đơn đặt vé.</AlertDescription>
            </Alert>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  const tickets = getTicketsByBookingId(bookingId);

  const getScheduleDetails = (scheduleId: string) => {
    const schedule = mockSchedules.find((s) => s.id === scheduleId);
    const route = mockRoutes.find((r) => r.id === schedule?.routeId);
    const vessel = mockVessels.find((v) => v.id === schedule?.vesselId);
    const fromPort = mockPorts.find((p) => p.id === route?.fromPortId);
    const toPort = mockPorts.find((p) => p.id === route?.toPortId);

    return { schedule, route, vessel, fromPort, toPort };
  };

  const handleDownloadPDF = (ticket: any) => {
    const details = getScheduleDetails(ticket.scheduleId);
    const pdfUrl = generateTicketPDF(
      ticket,
      booking,
      details.schedule,
      details.route,
      details.vessel,
      details.fromPort,
      details.toPort
    );

    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = `ticket-${ticket.id}.pdf`;
    link.click();
  };

  const outboundTickets = tickets.filter((t) => t.scheduleId === booking.outboundScheduleId);
  const returnTickets = booking.returnScheduleId
    ? tickets.filter((t) => t.scheduleId === booking.returnScheduleId)
    : [];

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="mb-6">
            <Link href="/me/bookings">
              <Button variant="ghost" size="sm" className="mb-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại danh sách
              </Button>
            </Link>

            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Vé tàu điện tử</h1>
                <p className="text-gray-600 mt-2">Mã đặt vé: {booking.code}</p>
              </div>
              <Badge className="bg-green-100 text-green-800">Đã xác nhận</Badge>
            </div>
          </div>

          <div className="space-y-6">
            {/* Outbound Tickets */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Vé chiều đi</h2>
              <div className="grid gap-4">
                {outboundTickets.map((ticket) => {
                  const details = getScheduleDetails(ticket.scheduleId);

                  return (
                    <Card key={ticket.id} className="overflow-hidden">
                      <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                        <CardTitle className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Ship className="h-5 w-5" />
                            {details.vessel?.name}
                          </div>
                          <div className="text-right">
                            <div className="text-sm opacity-90">Mã vé</div>
                            <div className="font-mono">{ticket.id.slice(-8).toUpperCase()}</div>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                          {/* Trip Info */}
                          <div className="lg:col-span-2 space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="text-center">
                                <div className="text-2xl font-bold">
                                  {details.schedule?.departureTime}
                                </div>
                                <div className="text-sm text-gray-600">
                                  {details.fromPort?.name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {details.fromPort?.city}
                                </div>
                              </div>
                              <div className="flex-1 mx-4">
                                <div className="border-t-2 border-dashed border-gray-300 relative">
                                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                                    {details.route?.estimatedDuration}p
                                  </div>
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold">
                                  {details.schedule?.arrivalTime}
                                </div>
                                <div className="text-sm text-gray-600">{details.toPort?.name}</div>
                                <div className="text-xs text-gray-500">{details.toPort?.city}</div>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                              <div className="space-y-2">
                                <div className="flex items-center gap-2 text-sm">
                                  <Calendar className="h-4 w-4 text-gray-500" />
                                  <span>{details.schedule?.date}</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                  <User className="h-4 w-4 text-gray-500" />
                                  <span>{ticket.passenger.name}</span>
                                </div>
                              </div>
                              <div className="space-y-2">
                                <div className="flex items-center gap-2 text-sm">
                                  <MapPin className="h-4 w-4 text-gray-500" />
                                  <span>Ghế {ticket.passenger.seatId}</span>
                                </div>
                                <div className="text-sm">
                                  <span className="text-gray-500">Loại vé: </span>
                                  <span>
                                    {ticket.passenger.type === 'adult' ? 'Người lớn' : 'Trẻ em'}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* QR Code */}
                          <div className="text-center border-l lg:border-l border-t lg:border-t-0 pt-4 lg:pt-0 lg:pl-6">
                            <div className="mb-4">
                              <Image
                                src={ticket.qrCode || '/placeholder.svg'}
                                alt="QR Code"
                                width={150}
                                height={150}
                                className="mx-auto border rounded"
                              />
                            </div>
                            <p className="text-xs text-gray-500 mb-4">Quét mã QR này khi lên tàu</p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownloadPDF(ticket)}
                              className="w-full"
                            >
                              <Download className="h-4 w-4 mr-2" />
                              Tải PDF
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>

            {/* Return Tickets */}
            {returnTickets.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Vé chiều về</h2>
                <div className="grid gap-4">
                  {returnTickets.map((ticket) => {
                    const details = getScheduleDetails(ticket.scheduleId);

                    return (
                      <Card key={ticket.id} className="overflow-hidden">
                        <CardHeader className="bg-gradient-to-r from-green-600 to-green-700 text-white">
                          <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Ship className="h-5 w-5" />
                              {details.vessel?.name}
                            </div>
                            <div className="text-right">
                              <div className="text-sm opacity-90">Mã vé</div>
                              <div className="font-mono">{ticket.id.slice(-8).toUpperCase()}</div>
                            </div>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6">
                          {/* Same content structure as outbound tickets */}
                          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <div className="lg:col-span-2 space-y-4">
                              <div className="flex items-center justify-between">
                                <div className="text-center">
                                  <div className="text-2xl font-bold">
                                    {details.schedule?.departureTime}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {details.fromPort?.name}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {details.fromPort?.city}
                                  </div>
                                </div>
                                <div className="flex-1 mx-4">
                                  <div className="border-t-2 border-dashed border-gray-300 relative">
                                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                                      {details.route?.estimatedDuration}p
                                    </div>
                                  </div>
                                </div>
                                <div className="text-center">
                                  <div className="text-2xl font-bold">
                                    {details.schedule?.arrivalTime}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {details.toPort?.name}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {details.toPort?.city}
                                  </div>
                                </div>
                              </div>

                              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2 text-sm">
                                    <Calendar className="h-4 w-4 text-gray-500" />
                                    <span>{details.schedule?.date}</span>
                                  </div>
                                  <div className="flex items-center gap-2 text-sm">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span>{ticket.passenger.name}</span>
                                  </div>
                                </div>
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2 text-sm">
                                    <MapPin className="h-4 w-4 text-gray-500" />
                                    <span>Ghế {ticket.passenger.seatId}</span>
                                  </div>
                                  <div className="text-sm">
                                    <span className="text-gray-500">Loại vé: </span>
                                    <span>
                                      {ticket.passenger.type === 'adult' ? 'Người lớn' : 'Trẻ em'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="text-center border-l lg:border-l border-t lg:border-t-0 pt-4 lg:pt-0 lg:pl-6">
                              <div className="mb-4">
                                <Image
                                  src={ticket.qrCode || '/placeholder.svg'}
                                  alt="QR Code"
                                  width={150}
                                  height={150}
                                  className="mx-auto border rounded"
                                />
                              </div>
                              <p className="text-xs text-gray-500 mb-4">
                                Quét mã QR này khi lên tàu
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownloadPDF(ticket)}
                                className="w-full"
                              >
                                <Download className="h-4 w-4 mr-2" />
                                Tải PDF
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Booking Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Thông tin đặt vé</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Mã đặt vé:</span>
                      <span className="font-mono">{booking.code}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Ngày đặt:</span>
                      <span>{new Date(booking.createdAt).toLocaleDateString('vi-VN')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Số hành khách:</span>
                      <span>{booking.passengers.length}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tổng tiền:</span>
                      <span className="font-semibold">
                        {booking.totalAmount.toLocaleString('vi-VN')}đ
                      </span>
                    </div>
                    {booking.discountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Giảm giá:</span>
                        <span>-{booking.discountAmount.toLocaleString('vi-VN')}đ</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600">Thanh toán:</span>
                      <span>
                        {booking.paymentMethod === 'card'
                          ? 'Thẻ tín dụng'
                          : booking.paymentMethod === 'e-wallet'
                          ? 'Ví điện tử'
                          : 'Chuyển khoản'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
