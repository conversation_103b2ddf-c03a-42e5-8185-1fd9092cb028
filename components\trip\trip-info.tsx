import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FerryService } from '@/lib/ferry-service';
import type { Port, Route, Schedule, Vessel } from '@/lib/types';
import { Car, Clock, Coffee, MapPin, Ship, Users, Utensils, Wifi } from 'lucide-react';
import type React from 'react';

interface TripInfoProps {
  schedule: Schedule;
  route: Route;
  vessel: Vessel;
  fromPort: Port;
  toPort: Port;
  searchQuery: {
    departureDate: string;
    adultCount: number;
    childCount: number;
    tripType: string;
  };
}

const amenityIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  WiFi: Wifi,
  'Quầy bar': Coffee,
  'Nhà hàng': Utensils,
  Toilet: Car,
  'Điều hòa': Car,
  'Cabin riêng': Car,
};

export function TripInfo({
  schedule,
  route,
  vessel,
  fromPort,
  toPort,
  searchQuery,
}: TripInfoProps) {
  const duration = FerryService.formatDuration(route.estimatedDuration);

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col items-center md:flex-row md:items-center md:justify-center">
          <div className="text-center md:text-center mx-auto">
            <CardTitle className="text-2xl mb-2">
              {fromPort.name} → {toPort.name}
            </CardTitle>
            <p className="text-gray-600">
              {searchQuery.departureDate} • {searchQuery.adultCount} người lớn
              {searchQuery.childCount > 0 && `, ${searchQuery.childCount} trẻ em`}
            </p>
          </div>
          <Badge variant="secondary" className="mt-2 md:mt-0 self-center">
            {searchQuery.tripType === 'round-trip' ? 'Khứ hồi' : 'Một chiều'}
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
          {/* Departure Info */}
          <div className="flex flex-col items-center justify-center text-center gap-2 border rounded-xl p-3 bg-white aspect-square md:aspect-auto md:flex-row md:items-center md:justify-start md:text-left md:space-x-3 md:border-0 md:rounded-none md:p-0 md:bg-transparent">
            <div className="bg-blue-100 p-3 rounded-full">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-xs md:text-sm text-gray-600">Khởi hành</p>
              <p className="text-base md:text-lg font-semibold">{schedule.departureTime}</p>
              <p className="text-xs md:text-sm text-gray-500">{fromPort.code}</p>
            </div>
          </div>

          {/* Arrival Info */}
          <div className="flex flex-col items-center justify-center text-center gap-2 border rounded-xl p-3 bg-white aspect-square md:aspect-auto md:flex-row md:items-center md:justify-start md:text-left md:space-x-3 md:border-0 md:rounded-none md:p-0 md:bg-transparent">
            <div className="bg-green-100 p-3 rounded-full">
              <MapPin className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-xs md:text-sm text-gray-600">Đến nơi</p>
              <p className="text-base md:text-lg font-semibold">{schedule.arrivalTime}</p>
              <p className="text-xs md:text-sm text-gray-500">{toPort.code}</p>
            </div>
          </div>

          {/* Duration */}
          <div className="flex flex-col items-center justify-center text-center gap-2 border rounded-xl p-3 bg-white aspect-square md:aspect-auto md:flex-row md:items-center md:justify-start md:text-left md:space-x-3 md:border-0 md:rounded-none md:p-0 md:bg-transparent">
            <div className="bg-purple-100 p-3 rounded-full">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-xs md:text-sm text-gray-600">Thời gian</p>
              <p className="text-base md:text-lg font-semibold">{duration}</p>
              <p className="text-xs md:text-sm text-gray-500">{route.distance}km</p>
            </div>
          </div>

          {/* Vessel Info */}
          <div className="flex flex-col items-center justify-center text-center gap-2 border rounded-xl p-3 bg-white aspect-square md:aspect-auto md:flex-row md:items-center md:justify-start md:text-left md:space-x-3 md:border-0 md:rounded-none md:p-0 md:bg-transparent">
            <div className="bg-orange-100 p-3 rounded-full">
              <Ship className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <p className="text-xs md:text-sm text-gray-600">Tàu</p>
              <p className="text-base md:text-lg font-semibold">{vessel.name}</p>
              <p className="text-xs md:text-sm text-gray-500">{vessel.code}</p>
            </div>
          </div>
        </div>

        {/* Vessel Details */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Thông tin tàu</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">Sức chứa: {vessel.capacity} hành khách</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Ship className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">Mã tàu: {vessel.code}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Tiện ích</h3>
              <div className="flex flex-wrap gap-2">
                {vessel.amenities.map((amenity) => {
                  const IconComponent = amenityIcons[amenity] || Car;
                  return (
                    <div
                      key={amenity}
                      className="flex items-center space-x-1 bg-gray-100 px-3 py-1 rounded-full"
                    >
                      <IconComponent className="h-3 w-3 text-gray-600" />
                      <span className="text-xs text-gray-700">{amenity}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
