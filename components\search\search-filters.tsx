'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { useState } from 'react';

export function SearchFilters() {
  const [priceRange, setPriceRange] = useState([100000, 500000]);
  const [timeRange, setTimeRange] = useState('all');
  const [amenities, setAmenities] = useState<string[]>([]);

  return (
    <div className="space-y-6">
      {/* Sort By */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Sắp xếp theo</CardTitle>
        </CardHeader>
        <CardContent>
          <Select defaultValue="departure-time">
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="departure-time">Giờ khởi hành</SelectItem>
              <SelectItem value="price-low">Giá thấp nhất</SelectItem>
              <SelectItem value="price-high">Giá cao nhất</SelectItem>
              <SelectItem value="duration">Thời gian ngắn nhất</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Price Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Khoảng giá</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Slider
              value={priceRange}
              onValueChange={setPriceRange}
              max={1000000}
              min={50000}
              step={50000}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{priceRange[0].toLocaleString('vi-VN')}đ</span>
              <span>{priceRange[1].toLocaleString('vi-VN')}đ</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Departure Time */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Giờ khởi hành</CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              <SelectItem value="morning">Sáng (06:00 - 12:00)</SelectItem>
              <SelectItem value="afternoon">Chiều (12:00 - 18:00)</SelectItem>
              <SelectItem value="evening">Tối (18:00 - 24:00)</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Amenities */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tiện ích</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {['WiFi', 'Điều hòa', 'Quầy bar', 'Nhà hàng', 'Toilet', 'Cabin riêng'].map(
              (amenity) => (
                <div key={amenity} className="flex items-center space-x-2">
                  <Checkbox
                    id={amenity}
                    checked={amenities.includes(amenity)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setAmenities([...amenities, amenity]);
                      } else {
                        setAmenities(amenities.filter((a) => a !== amenity));
                      }
                    }}
                  />
                  <Label htmlFor={amenity} className="text-sm">
                    {amenity}
                  </Label>
                </div>
              )
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
