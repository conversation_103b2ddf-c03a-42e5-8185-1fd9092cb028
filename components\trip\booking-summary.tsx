'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { FerryService } from '@/lib/ferry-service';
import type { PassengerType, Port, Schedule } from '@/lib/types';
import { Clock, MapPin, Users } from 'lucide-react';
import Link from 'next/link';
import { ShinyButton } from '../magicui/shiny-button';

interface SelectedSeat {
  seat: {
    id: string;
    number: string;
    price: {
      adult: number;
      child: number;
    };
  };
  passengerType: PassengerType;
  passengerIndex: number;
}

interface BookingSummaryProps {
  schedule: Schedule;
  fromPort: Port;
  toPort: Port;
  selectedSeats: SelectedSeat[];
  searchQuery: {
    fromPortId: string;
    toPortId: string;
    departureDate: string;
    returnDate?: string;
    tripType: string;
    adultCount: number;
    childCount: number;
  };
  totalAmount: number;
}

export function BookingSummary({
  schedule,
  fromPort,
  toPort,
  selectedSeats,
  searchQuery,
  totalAmount,
}: BookingSummaryProps) {
  const totalPassengers = searchQuery.adultCount + searchQuery.childCount;
  const canProceed = selectedSeats.length === totalPassengers;

  const adultSeats = selectedSeats.filter((s) => s.passengerType === 'adult');
  const childSeats = selectedSeats.filter((s) => s.passengerType === 'child');

  const checkoutUrl = canProceed
    ? `/cart?${new URLSearchParams({
        scheduleId: schedule.id,
        seats: selectedSeats.map((s) => `${s.seat.id}:${s.passengerType}`).join(','),
        from: searchQuery.fromPortId,
        to: searchQuery.toPortId,
        departure: searchQuery.departureDate,
        adults: searchQuery.adultCount.toString(),
        children: searchQuery.childCount.toString(),
        type: searchQuery.tripType,
        ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
      }).toString()}`
    : '#';

  return (
    <div className="sticky top-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Tóm tắt đặt vé</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Trip Info */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {fromPort.name} → {toPort.name}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {searchQuery.departureDate} • {schedule.departureTime}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {searchQuery.adultCount} người lớn
                {searchQuery.childCount > 0 && `, ${searchQuery.childCount} trẻ em`}
              </span>
            </div>
          </div>

          <Separator />

          {/* Selected Seats */}
          <div>
            <h3 className="font-semibold mb-3">
              Ghế đã chọn ({selectedSeats.length}/{totalPassengers})
            </h3>
            {selectedSeats.length === 0 ? (
              <p className="text-sm text-gray-500">Chưa chọn ghế nào</p>
            ) : (
              <div className="space-y-2">
                {adultSeats.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Người lớn:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {adultSeats.map((selectedSeat) => (
                        <span
                          key={selectedSeat.seat.id}
                          className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                        >
                          {selectedSeat.seat.number}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {childSeats.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Trẻ em:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {childSeats.map((selectedSeat) => (
                        <span
                          key={selectedSeat.seat.id}
                          className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded"
                        >
                          {selectedSeat.seat.number}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <Separator />

          {/* Price Breakdown */}
          <div className="space-y-2">
            <h3 className="font-semibold">Chi tiết giá</h3>
            {adultSeats.map((selectedSeat) => (
              <div key={selectedSeat.seat.id} className="flex justify-between text-sm">
                <span>Ghế {selectedSeat.seat.number} (Người lớn)</span>
                <span>{FerryService.formatCurrency(selectedSeat.seat.price.adult)}</span>
              </div>
            ))}
            {childSeats.map((selectedSeat) => (
              <div key={selectedSeat.seat.id} className="flex justify-between text-sm">
                <span>Ghế {selectedSeat.seat.number} (Trẻ em)</span>
                <span>{FerryService.formatCurrency(selectedSeat.seat.price.child)}</span>
              </div>
            ))}
            <Separator />
            <div className="flex justify-between font-semibold">
              <span>Tổng cộng</span>
              <span className="text-blue-600">{FerryService.formatCurrency(totalAmount)}</span>
            </div>
          </div>

          {/* Action Button */}
          <div className="pt-4">
            {canProceed ? (
              <Link href={checkoutUrl}>
                <ShinyButton className="w-full font-medium bg-blue-600 hover:bg-blue-700">
                  <span className="flex items-center justify-center text-white py-1">
                    Tiếp tục đặt vé
                  </span>
                </ShinyButton>
              </Link>
            ) : (
              <Button disabled className="w-full" size="lg">
                Vui lòng chọn {totalPassengers - selectedSeats.length} ghế nữa
              </Button>
            )}
          </div>

          {/* Help Text */}
          <p className="text-xs text-gray-500 text-center">
            Ghế sẽ được giữ trong 15 phút sau khi chọn
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
