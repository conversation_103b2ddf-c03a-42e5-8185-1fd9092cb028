'use client';

import { showSuccessToast } from '@/components/ui/soft-toasts';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { useState } from 'react';

export default function PromotionsBanner() {
  const [copiedCode, setCopiedCode] = useState<string>('');

  const promoCodes = [
    { code: 'WELCOME20', discount: '20%', type: 'Khách hàng mới' },
    { code: 'ROUNDTRIP15', discount: '15%', type: 'Vé khứ hồi' },
    { code: 'FAMILY50', discount: '50%', type: '<PERSON>ia đình' },
  ];

  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      showSuccessToast(`Đã copy mã: ${code}`);
      setTimeout(() => setCopiedCode(''), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <section className="py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ scale: 0.95, opacity: 0, y: 30 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="bg-gradient-to-br from-red-500 via-red-600 to-red-700 
                     rounded-2xl p-8 text-white text-center relative overflow-hidden 
                     shadow-2xl shadow-red-500/25"
        >
          {/* Animated Background Elements */}
          <motion.div
            className="absolute -top-8 -right-8 w-24 h-24 bg-white/20 rounded-full"
            animate={{
              scale: [1, 1.3, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              repeat: Infinity,
              duration: 4,
              ease: 'easeInOut',
            }}
          />
          <motion.div
            className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/15 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [360, 180, 0],
            }}
            transition={{
              repeat: Infinity,
              duration: 5,
              ease: 'easeInOut',
              delay: 1,
            }}
          />
          <motion.div
            className="absolute top-1/2 right-1/4 w-16 h-16 bg-yellow-300/30 rounded-full"
            animate={{
              y: [-10, 10, -10],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              repeat: Infinity,
              duration: 3,
              ease: 'easeInOut',
            }}
          />

          {/* Content */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <motion.h2
              className="text-2xl md:text-3xl font-bold mb-4 flex justify-center items-center gap-3"
              whileHover={{ scale: 1.02 }}
            >
              <motion.div
                animate={{ rotate: [0, 15, -15, 0] }}
                transition={{ repeat: Infinity, duration: 2 }}
              >
                <Sparkles className="h-8 w-8 text-yellow-300" />
              </motion.div>
              Khuyến mãi đặc biệt
              <motion.div
                animate={{ rotate: [0, -15, 15, 0] }}
                transition={{ repeat: Infinity, duration: 2, delay: 1 }}
              >
                <Sparkles className="h-8 w-8 text-yellow-300" />
              </motion.div>
            </motion.h2>

            <motion.p
              className="text-lg mb-8 text-red-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              Giảm đến 20% cho khách hàng mới • Ưu đãi vé khứ hồi 15%
            </motion.p>
          </motion.div>

          {/* Promo Codes */}
          <div className="flex flex-wrap justify-center gap-4">
            {promoCodes.map((promo, index) => (
              <motion.button
                key={promo.code}
                onClick={() => copyToClipboard(promo.code)}
                className="group relative bg-white/95 backdrop-blur-sm text-red-600 
                          px-6 py-3 rounded-full font-bold shadow-lg
                          hover:bg-white hover:shadow-xl
                          transition-all duration-300 overflow-hidden
                          border-2 border-transparent hover:border-yellow-300"
                initial={{ y: 30, opacity: 0, scale: 0.8 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                transition={{
                  delay: 0.7 + index * 0.15,
                  duration: 0.5,
                  type: 'spring',
                  stiffness: 100,
                }}
                whileHover={{
                  scale: 1.05,
                  y: -2,
                  boxShadow: '0 8px 25px rgba(255,255,255,0.4)',
                }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Shine effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent 
                            via-white/40 to-transparent -translate-x-full"
                  animate={{
                    x: copiedCode === promo.code ? ['-100%', '100%'] : '-100%',
                  }}
                  transition={{ duration: 0.6 }}
                />

                <div className="relative z-10 flex items-center justify-center">
                  <span className="text-lg">{promo.code}</span>
                </div>

                {/* Tooltip */}
                <div
                  className="absolute -top-12 left-1/2 transform -translate-x-1/2
                               opacity-0 group-hover:opacity-100 transition-all duration-300
                               bg-gray-900 text-white text-xs px-3 py-1 rounded-lg
                               whitespace-nowrap pointer-events-none"
                >
                  {promo.type} - Giảm {promo.discount}
                  <div
                    className="absolute top-full left-1/2 transform -translate-x-1/2
                                 border-4 border-transparent border-t-gray-900"
                  ></div>
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
