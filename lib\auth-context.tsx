'use client';

import { createContext, useContext, useEffect, useState, type ReactNode } from 'react';

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: 'customer' | 'admin';
  createdAt: Date;
}

interface AuthContextType {
  user: User | null;
  logout: () => void;
  isLoading: boolean;
  setSession: (session: {
    user: { id: string; name: string; email: string; phone?: string; role_id?: number };
    token?: string;
  }) => void;
}

// RegisterData removed; registration handled at page level

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Removed mock users; auth now relies on real API responses

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const USER_KEY = 'auth_user';
  const TOKEN_KEY = 'auth_token';

  const mapApiUserToUser = (apiUser: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    role_id?: number;
    role?: 'customer' | 'admin';
    createdAt?: string | Date;
  }): User => {
    const roleId = apiUser.role_id;
    const role: 'customer' | 'admin' = apiUser.role
      ? apiUser.role
      : roleId === 1 || roleId === 2
      ? 'admin'
      : 'customer';

    const createdAt = apiUser.createdAt ? new Date(apiUser.createdAt) : new Date();

    return {
      id: apiUser.id,
      email: apiUser.email,
      name: apiUser.name,
      phone: apiUser.phone,
      role,
      createdAt,
    };
  };

  useEffect(() => {
    // Check for stored user session (migrate from old key if needed)
    const storedNew = localStorage.getItem(USER_KEY);
    const storedOld = localStorage.getItem('ferry_user');

    const stored = storedNew || storedOld;
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        const mapped = mapApiUserToUser(parsed);
        setUser(mapped);
        // migrate to new key if coming from old
        if (!storedNew) {
          localStorage.setItem(
            USER_KEY,
            JSON.stringify({ ...mapped, createdAt: mapped.createdAt.toISOString() })
          );
          localStorage.removeItem('ferry_user');
        }
      } catch (error) {
        localStorage.removeItem(USER_KEY);
        localStorage.removeItem('ferry_user');
      }
    }
    setIsLoading(false);
  }, []);

  // Login/register flows are handled directly in pages via API routes.

  const logout = () => {
    setUser(null);
    localStorage.removeItem(USER_KEY);
    localStorage.removeItem(TOKEN_KEY);
  };

  const setSession: AuthContextType['setSession'] = ({ user: apiUser, token }) => {
    const mapped = mapApiUserToUser(apiUser);
    setUser(mapped);
    localStorage.setItem(
      USER_KEY,
      JSON.stringify({ ...mapped, createdAt: mapped.createdAt.toISOString() })
    );
    if (token) {
      localStorage.setItem(TOKEN_KEY, token);
    }
  };

  return (
    <AuthContext.Provider value={{ user, logout, isLoading, setSession }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
