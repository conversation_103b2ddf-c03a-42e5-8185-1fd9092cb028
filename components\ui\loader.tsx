type Props = {
  centered?: boolean;
  size?: number | string;
  className?: string;
};

export default function ClassicLoader({ centered = true, size = 40, className = '' }: Props) {
  const spinnerSize = typeof size === 'number' ? `${size}px` : size;
  const spinnerStyles = {
    height: spinnerSize,
    width: spinnerSize,
  } as React.CSSProperties;

  const spinner = (
    <div
      className={`border-primary animate-spin items-center justify-center rounded-full border-4 border-t-transparent ${className}`}
      style={spinnerStyles}
    />
  );

  if (centered) {
    return (
      <div className="flex items-center justify-center min-h-screen min-h-[50vh]">{spinner}</div>
    );
  }

  return spinner;
}
