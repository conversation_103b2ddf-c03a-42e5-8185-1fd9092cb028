import type { Metadata } from 'next';
import Script from 'next/script';
import CrispVisibility from './crisp-visibility';

export const metadata: Metadata = {
  title: 'Hỗ trợ khách hàng - Ocean Pass',
};

export default function SupportLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}

      {/* Crisp chỉ tải ở /faq */}
      <Script id="crisp-faq" strategy="afterInteractive">
        {`
          window.$crisp = window.$crisp || [];
          window.CRISP_WEBSITE_ID = "${process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID}";
          (function () {
            var d = document;
            var s = d.createElement("script");
            s.src = "https://client.crisp.chat/l.js";
            s.async = 1;
            d.getElementsByTagName("head")[0].appendChild(s);
          })();
        `}
      </Script>

      {/* Quản lý hiển thị widget */}
      <CrispVisibility />
    </>
  );
}
