// Ocean Pass System Types

export type TripType = 'one-way' | 'round-trip';
export type PassengerType = 'adult' | 'child';
export type SeatStatus = 'available' | 'reserved' | 'booked';
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed';
export type PaymentMethod = 'card' | 'e-wallet' | 'bank-transfer';

export interface Port {
  id: string;
  name: string;
  code: string;
  city: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface Route {
  id: string;
  fromPortId: string;
  toPortId: string;
  distance: number; // in km
  estimatedDuration: number; // in minutes
}

export interface Vessel {
  id: string;
  name: string;
  code: string;
  capacity: number;
  amenities: string[];
  seatMapId: string;
}

export interface SeatMap {
  id: string;
  vesselId: string;
  decks: Deck[];
}

export interface Deck {
  id: string;
  name: string;
  level: number;
  sections: Section[];
}

export interface Section {
  id: string;
  name: string;
  type: 'economy' | 'business' | 'vip' | 'cabin';
  rows: Row[];
}

export interface Row {
  id: string;
  number: number;
  seats: Seat[];
}

export interface Seat {
  id: string;
  number: string;
  type: 'seat' | 'cabin';
  status: SeatStatus;
  price: {
    adult: number;
    child: number;
  };
}

export interface Schedule {
  id: string;
  routeId: string;
  vesselId: string;
  departureTime: string;
  arrivalTime: string;
  date: string;
  basePrice: {
    adult: number;
    child: number;
  };
  status: 'active' | 'cancelled' | 'full';
}

export interface Passenger {
  id: string;
  name: string;
  age: number;
  type: PassengerType;
  seatId: string;
}

export interface Booking {
  id: string;
  code: string;
  userId?: string;
  tripType: TripType;
  outboundScheduleId: string;
  returnScheduleId?: string;
  passengers: Passenger[];
  totalAmount: number;
  discountAmount: number;
  promotionCode?: string;
  status: BookingStatus;
  paymentMethod?: PaymentMethod;
  createdAt: string;
  updatedAt: string;
}

export interface Ticket {
  id: string;
  bookingId: string;
  scheduleId: string;
  passenger: Passenger;
  qrCode: string;
  isUsed: boolean;
}

export interface Promotion {
  id: string;
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed';
  value: number;
  minAmount?: number;
  maxDiscount?: number;
  validFrom: string;
  validTo: string;
  isActive: boolean;
}

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  dateOfBirth?: string;
  createdAt: string;
}

// Search and Filter Types
export interface SearchParams {
  fromPortId: string;
  toPortId: string;
  departureDate: string;
  returnDate?: string;
  tripType: TripType;
  adultCount: number;
  childCount: number;
}

export interface TripSearchResult {
  schedule: Schedule;
  route: Route;
  vessel: Vessel;
  fromPort: Port;
  toPort: Port;
  availableSeats: number;
  minPrice: {
    adult: number;
    child: number;
  };
}
