import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useAuth } from '@/lib/auth-context';
import { LogOut, Menu, Package, Ship, User } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { NavMenu } from './nav-menu';

export const NavigationSheet = () => {
  const { user, logout } = useAuth();
  const router = useRouter();

  return (
    <Sheet modal={false}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="rounded-full">
          <Menu />
        </Button>
      </SheetTrigger>
      <SheetContent className="px-6 py-3" aria-label="Menu điều hướng">
        <SheetTitle className="sr-only"><PERSON>u điề<PERSON> hướng</SheetTitle>
        <Link href="/" className="flex items-center space-x-2">
          <Ship className="h-8 w-8 text-blue-600" />
          <span className="text-xl font-bold text-gray-900">Ocean Pass</span>
        </Link>
        <NavMenu orientation="vertical" className="mt-6 [&>div]:h-full" />

        <Separator className="my-4" />

        {!user ? (
          <div className="flex flex-col gap-3">
            <Link href="/auth/login">
              <Button variant="outline" className="w-full rounded-full text-base">
                Đăng nhập
              </Button>
            </Link>
            <Link href="/auth/register">
              <Button className="w-full rounded-full text-base">đăng ký</Button>
            </Link>
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-3 mb-2">
              <div className="h-10 w-10 rounded-full bg-slate-200 flex items-center justify-center overflow-hidden">
                {/* show initial when no avatar provided */}
                <span className="text-sm font-semibold text-slate-700">
                  {(user.name || 'U').charAt(0)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground truncate">{user.email}</p>
              </div>
            </div>
            <Link href="/me/bookings">
              <Button variant="ghost" className="w-full justify-start rounded-full py-3 px-4 gap-3">
                <Package className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Đơn vé của tôi</span>
              </Button>
            </Link>
            <Link href="/me/profile">
              <Button variant="ghost" className="w-full justify-start rounded-full py-3 px-4 gap-3">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Hồ sơ cá nhân</span>
              </Button>
            </Link>
            {user.role === 'admin' && (
              <Link href="/admin">
                <Button variant="ghost" className="justify-start">
                  Trang quản trị
                </Button>
              </Link>
            )}
            <Separator className="my-2" />
            <Button
              variant="destructive"
              className="justify-start text-white justify-center"
              onClick={() => {
                logout();
                router.push('/');
              }}
            >
              <LogOut className="mr-2 h-4 w-4" /> Đăng xuất
            </Button>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};
