'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import type { Promotion } from '@/lib/types';
import { Calendar, Check, Copy, Gift, Percent } from 'lucide-react';

export default function PromotionCard({
  promotion,
  copiedCode,
  onCopyCode,
  isExpiring,
}: {
  promotion: Promotion;
  copiedCode: string | null;
  onCopyCode: (code: string) => void;
  isExpiring?: boolean;
}) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getPromotionIcon = (type: string) => {
    return type === 'percentage' ? <Percent className="h-4 w-4" /> : <Gift className="h-4 w-4" />;
  };

  const getPromotionValue = (promotion: Promotion) => {
    if (promotion.type === 'percentage') {
      return `Giảm ${promotion.value}%`;
    }
    return `Giảm ${formatCurrency(promotion.value)}`;
  };

  const getPromotionCondition = (promotion: Promotion) => {
    let condition = `Đơn hàng từ ${formatCurrency(promotion.minAmount || 0)}`;
    if (promotion.type === 'percentage' && promotion.maxDiscount) {
      condition += ` • Giảm tối đa ${formatCurrency(promotion.maxDiscount)}`;
    }
    return condition;
  };

  // Defensive: hide if already expired
  const now = new Date();
  const to = new Date(promotion.validTo);
  if (now > to) return null;

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-blue-50 rounded-lg text-blue-600">
              {getPromotionIcon(promotion.type)}
            </div>
            {isExpiring ? (
              <Badge variant="secondary" className="bg-amber-50 text-amber-700">
                Sắp hết hạn
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-green-50 text-green-700">
                Đang hoạt động
              </Badge>
            )}
          </div>
        </div>
        <CardTitle className="text-lg">{promotion.name}</CardTitle>
        <CardDescription>{promotion.description}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="text-center py-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {getPromotionValue(promotion)}
          </div>
          <div className="text-sm text-gray-600">{getPromotionCondition(promotion)}</div>
        </div>

        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border border-dashed border-gray-300">
          <div className="flex-1">
            <div className="text-xs text-gray-500 mb-1">Mã khuyến mãi</div>
            <div className="font-mono font-bold text-lg">{promotion.code}</div>
          </div>
          <Button variant="outline" size="sm" onClick={() => onCopyCode(promotion.code)}>
            {copiedCode === promotion.code ? (
              <Check className="h-4 w-4 text-green-600" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </Button>
        </div>

        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Calendar className="h-4 w-4" />
          <span>
            Từ {formatDate(promotion.validFrom)} đến {formatDate(promotion.validTo)}
          </span>
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button className="w-full">Xem chi tiết</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <div className="p-2 bg-blue-50 rounded-lg text-blue-600">
                  {getPromotionIcon(promotion.type)}
                </div>
                <span>{promotion.name}</span>
              </DialogTitle>
              <DialogDescription>Chi tiết chương trình khuyến mãi</DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Mô tả</h4>
                <p className="text-gray-600">{promotion.description}</p>
              </div>

              <Separator />

              <div>
                <h4 className="font-semibold mb-2">Điều kiện áp dụng</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Giá trị đơn hàng tối thiểu: {formatCurrency(promotion.minAmount || 0)}</li>
                  {promotion.type === 'percentage' && promotion.maxDiscount && (
                    <li>• Giảm giá tối đa: {formatCurrency(promotion.maxDiscount)}</li>
                  )}
                  <li>
                    • Thời gian: {formatDate(promotion.validFrom)} - {formatDate(promotion.validTo)}
                  </li>
                  <li>• Áp dụng cho tất cả tuyến đường</li>
                </ul>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">Cách sử dụng</h4>
                <ol className="space-y-1 text-sm text-gray-600">
                  <li>1. Chọn chuyến tàu và ghế ngồi</li>
                  <li>
                    2. Nhập mã <strong>{promotion.code}</strong> tại trang thanh toán
                  </li>
                  <li>3. Kiểm tra giá và hoàn tất thanh toán</li>
                </ol>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
