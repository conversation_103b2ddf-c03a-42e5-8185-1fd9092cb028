'use client';

import { cn } from '@/lib/utils';
import {
  BarChart3,
  Calendar,
  ChevronLeft,
  ChevronRight,
  FileText,
  LayoutDashboard,
  MapPin,
  Ship,
  Tag,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { name: 'Cảng & Tuyến', href: '/admin/ports', icon: MapPin },
  { name: '<PERSON><PERSON><PERSON> & <PERSON><PERSON> đồ ghế', href: '/admin/vessels', icon: Ship },
  { name: '<PERSON><PERSON><PERSON> trình', href: '/admin/schedules', icon: Calendar },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi', href: '/admin/promotions', icon: Tag },
  { name: 'Đơn đặt vé', href: '/admin/bookings', icon: FileText },
  { name: '<PERSON><PERSON><PERSON><PERSON> hàng', href: '/admin/customers', icon: Users },
  { name: 'Báo cáo', href: '/admin/reports', icon: BarChart3 },
];

export function AdminSidebar() {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const mediaQuery = window.matchMedia('(max-width: 767px)');
    const applyFromMediaQuery = () => setCollapsed(mediaQuery.matches);
    // Set initial based on current viewport
    applyFromMediaQuery();
    // Listen to viewport changes
    mediaQuery.addEventListener('change', applyFromMediaQuery);
    return () => mediaQuery.removeEventListener('change', applyFromMediaQuery);
  }, []);

  return (
    <div
      className={cn(
        'bg-white border-r border-gray-200 min-h-screen transition-all duration-300 ease-in-out',
        collapsed ? 'w-20' : 'w-64'
      )}
    >
      <div className="flex items-center justify-between px-4 pt-4">
        <span className={cn('text-sm font-semibold text-gray-700', collapsed && 'sr-only')}>
          Menu
        </span>
        <button
          type="button"
          onClick={() => setCollapsed((v) => !v)}
          className="inline-flex items-center justify-center rounded-md p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors"
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {collapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
        </button>
      </div>
      <nav className="mt-4 px-2">
        <ul className="space-y-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href;
            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
                    collapsed && 'justify-center'
                  )}
                  title={item.name}
                >
                  <item.icon className={cn('h-5 w-5', !collapsed && 'mr-1')} />
                  {!collapsed && <span className="truncate">{item.name}</span>}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}
