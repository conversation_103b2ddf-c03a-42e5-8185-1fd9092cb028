'use client';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { FerryService } from '@/lib/ferry-service';
import { getPortById, getRouteById, getScheduleById, getVesselById } from '@/lib/mock-data';
import type { Port, Route, Schedule, Vessel } from '@/lib/types';
import { CheckCircle, Clock, Download, MapPin, Printer, QrCode, User } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

function ConfettiExplosion() {
  const confettiCount = 100;
  const colors = ['#ef4444', '#3b82f6', '#22c55e', '#eab308', '#8b5cf6', '#f97316'];
  return (
    <>
      <style>
        {`
          @keyframes fall {
            0% {
                transform: translateY(-10vh) rotate(0deg);
                opacity: 1;
            }
            100% {
              transform: translateY(110vh) rotate(720deg);
              opacity: 0;
            }
          }
        `}
      </style>
      <div className="fixed inset-0 z-10 pointer-events-none" aria-hidden="true">
        {Array.from({ length: confettiCount }).map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-4"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${-20 + Math.random() * 10}%`,
              backgroundColor: colors[i % colors.length],
              transform: `rotate(${Math.random() * 360}deg)`,
              animation: `fall ${2.5 + Math.random() * 2.5}s ${Math.random() * 2}s linear forwards`,
            }}
          />
        ))}
      </div>
    </>
  );
}

export default function SuccessPage() {
  const searchParams = useSearchParams();

  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [route, setRoute] = useState<Route | null>(null);
  const [vessel, setVessel] = useState<Vessel | null>(null);
  const [fromPort, setFromPort] = useState<Port | null>(null);
  const [toPort, setToPort] = useState<Port | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showConfetti, setShowConfetti] = useState(false);

  // Parse URL parameters
  const bookingCode = searchParams.get('bookingCode') || '';
  const scheduleId = searchParams.get('scheduleId') || '';
  const seatsParam = searchParams.get('seats') || '';
  const passengersParam = searchParams.get('passengers') || '';
  const searchQuery = {
    fromPortId: searchParams.get('from') || '',
    toPortId: searchParams.get('to') || '',
    departureDate: searchParams.get('departure') || '',
    returnDate: searchParams.get('return') || '',
    tripType: (searchParams.get('type') as 'one-way' | 'round-trip') || 'one-way',
    adultCount: Number.parseInt(searchParams.get('adults') || '1'),
    childCount: Number.parseInt(searchParams.get('children') || '0'),
  };

  const subtotal = Number.parseFloat(searchParams.get('subtotal') || '0');
  const discount = Number.parseFloat(searchParams.get('discount') || '0');
  const total = Number.parseFloat(searchParams.get('total') || '0');
  const promoCode = searchParams.get('promoCode') || '';
  const paymentMethod = searchParams.get('paymentMethod') || '';

  const passengers = passengersParam ? JSON.parse(passengersParam) : [];
  const seats = seatsParam
    ? seatsParam.split(',').map((pair) => {
        const [seatId, passengerType] = pair.split(':');
        return { seatId, passengerType };
      })
    : [];

  useEffect(() => {
    const scheduleData = getScheduleById(scheduleId);
    if (scheduleData) {
      const routeData = getRouteById(scheduleData.routeId);
      const vesselData = getVesselById(scheduleData.vesselId);
      const fromPortData = getPortById(routeData?.fromPortId || '');
      const toPortData = getPortById(routeData?.toPortId || '');

      setSchedule(scheduleData);
      setRoute(routeData || null);
      setVessel(vesselData || null);
      setFromPort(fromPortData || null);
      setToPort(toPortData || null);
    }
    setIsLoading(false);
  }, [scheduleId]);

  useEffect(() => {
    const mountTimer = setTimeout(() => setShowConfetti(true), 100);
    const unmountTimer = setTimeout(() => setShowConfetti(false), 6000);
    return () => {
      clearTimeout(mountTimer);
      clearTimeout(unmountTimer);
    };
  }, []);

  const handleDownloadPDF = () => {
    // In a real app, this would generate and download a PDF ticket
    alert('Tính năng tải PDF sẽ được triển khai trong phiên bản tiếp theo');
  };

  const handlePrintTicket = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {showConfetti && <ConfettiExplosion />}
      <Navbar04Page />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
        {/* Success Header */}
        <div className="text-center mb-8">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Đặt vé thành công!</h1>
          <p className="text-gray-600">Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Booking Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Booking Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Thông tin đặt vé</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Đã xác nhận
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Mã đặt vé</p>
                    <p className="font-semibold text-lg">{bookingCode}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Ngày đặt</p>
                    <p className="font-semibold">{new Date().toLocaleDateString('vi-VN')}</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">
                      {fromPort?.name} → {toPort?.name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span>
                      {searchQuery.departureDate} • {schedule?.departureTime} -{' '}
                      {schedule?.arrivalTime}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span>
                      {searchQuery.adultCount} người lớn
                      {searchQuery.childCount > 0 && `, ${searchQuery.childCount} trẻ em`}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Passenger List */}
            <Card>
              <CardHeader>
                <CardTitle>Danh sách hành khách</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {passengers.map((passenger: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <span className="text-sm font-semibold text-blue-600">
                            {passenger.seatNumber}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{passenger.name}</p>
                          <p className="text-sm text-gray-600">
                            {passenger.age} tuổi •{' '}
                            {passenger.type === 'adult' ? 'Người lớn' : 'Trẻ em'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">Ghế {passenger.seatNumber}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* QR Codes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <QrCode className="h-5 w-5" />
                  <span>Vé điện tử</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {passengers.map((passenger: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4 text-center">
                      <div className="w-32 h-32 mx-auto mb-3 bg-gray-100 rounded-lg flex items-center justify-center">
                        <img
                          src={
                            FerryService.generateQRCode(bookingCode, passenger.name) ||
                            '/placeholder.svg'
                          }
                          alt={`QR Code for ${passenger.name}`}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <p className="font-medium text-sm">{passenger.name}</p>
                      <p className="text-xs text-gray-600">Ghế {passenger.seatNumber}</p>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 text-center mt-4">
                  Vui lòng xuất trình QR code này khi lên tàu
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Actions & Summary */}
          <div className="lg:col-span-1 space-y-6">
            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Hành động</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col gap-2">
                <Button
                  onClick={handleDownloadPDF}
                  className="w-full bg-transparent"
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Tải vé PDF
                </Button>
                <Button
                  onClick={handlePrintTicket}
                  className="w-full bg-transparent"
                  variant="outline"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  In vé
                </Button>
                <Link href="/me/bookings">
                  <Button className="w-full bg-transparent" variant="outline">
                    Xem đơn đặt vé
                  </Button>
                </Link>
                <Link href="/">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">Đặt vé mới</Button>
                </Link>
              </CardContent>
            </Card>

            {/* Payment Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Chi tiết thanh toán</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Tạm tính</span>
                  <span>{FerryService.formatCurrency(subtotal)}</span>
                </div>
                {discount > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Giảm giá {promoCode && `(${promoCode})`}</span>
                    <span>-{FerryService.formatCurrency(discount)}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between font-semibold">
                  <span>Đã thanh toán</span>
                  <span className="text-blue-600">{FerryService.formatCurrency(total)}</span>
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  Phương thức:{' '}
                  {paymentMethod === 'card'
                    ? 'Thẻ tín dụng'
                    : paymentMethod === 'e-wallet'
                    ? 'Ví điện tử'
                    : 'Chuyển khoản'}
                </div>
              </CardContent>
            </Card>

            {/* Important Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Lưu ý quan trọng</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-gray-600 space-y-2">
                <p>• Vui lòng có mặt tại cảng trước 30 phút</p>
                <p>• Mang theo giấy tờ tùy thân hợp lệ</p>
                <p>• Xuất trình QR code khi lên tàu</p>
                <p>• Liên hệ 1900 1234 nếu cần hỗ trợ</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
