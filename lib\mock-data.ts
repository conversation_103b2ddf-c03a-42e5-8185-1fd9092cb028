import type { Booking, Port, Row, Schedule, Seat, SeatMap, User, Vessel } from './types';

// Mock Ports
export const mockPorts: Port[] = [
  {
    id: 'port-1',
    name: 'Cảng Bạch Đằng',
    code: 'HCM',
    city: 'TP<PERSON> H<PERSON> Chí Minh',
    address: 'Quận 1, TP<PERSON> H<PERSON> Chí Minh',
    coordinates: { lat: 10.7769, lng: 106.7009 },
  },
  {
    id: 'port-2',
    name: 'Cảng Cần Thơ',
    code: 'CTH',
    city: 'Cần Thơ',
    address: 'Quận Ninh Kiều, Cần Thơ',
    coordinates: { lat: 10.0452, lng: 105.7469 },
  },
  {
    id: 'port-3',
    name: 'Cảng Vũng Tàu',
    code: 'VTU',
    city: 'Vũng Tàu',
    address: 'TP. Vũng Tàu, Bà Rịa - Vũng Tàu',
    coordinates: { lat: 10.346, lng: 107.0843 },
  },
  {
    id: 'port-4',
    name: '<PERSON><PERSON>ng Phú Quốc',
    code: 'PQC',
    city: 'Phú Quốc',
    address: 'Dương Đông, Phú Quốc, Kiên Giang',
    coordinates: { lat: 10.2899, lng: 103.984 },
  },
  {
    id: 'port-5',
    name: 'Cảng Rạch Giá',
    code: 'RGI',
    city: 'Rạch Giá',
    address: 'TP. Rạch Giá, Kiên Giang',
    coordinates: { lat: 10.012, lng: 105.0802 },
  },
];

// Mock Routes
// Removed mockRoutes; use database via /api/routes

// Mock Vessels
export const mockVessels: Vessel[] = [
  {
    id: 'vessel-1',
    name: 'Sài Gòn Express',
    code: 'SGE-001',
    capacity: 200,
    amenities: ['WiFi', 'Điều hòa', 'Quầy bar', 'Toilet'],
    seatMapId: 'seatmap-1',
  },
  {
    id: 'vessel-2',
    name: 'Mekong Star',
    code: 'MKS-002',
    capacity: 150,
    amenities: ['WiFi', 'Điều hòa', 'Toilet'],
    seatMapId: 'seatmap-2',
  },
  {
    id: 'vessel-3',
    name: 'Phú Quốc Pearl',
    code: 'PQP-003',
    capacity: 300,
    amenities: ['WiFi', 'Điều hòa', 'Nhà hàng', 'Quầy bar', 'Toilet', 'Cabin riêng'],
    seatMapId: 'seatmap-3',
  },
];

// Generate mock seat data
const generateSeats = (rowNumber: number, seatsPerRow: number, sectionType: string): Seat[] => {
  const seats: Seat[] = [];
  const basePrice =
    sectionType === 'economy' ? 200000 : sectionType === 'business' ? 350000 : 500000;

  for (let i = 1; i <= seatsPerRow; i++) {
    const seatLetter = String.fromCharCode(64 + i); // A, B, C, D...
    seats.push({
      id: `seat-${rowNumber}${seatLetter}`,
      number: `${rowNumber}${seatLetter}`,
      type: sectionType === 'cabin' ? 'cabin' : 'seat',
      status: Math.random() > 0.7 ? 'booked' : Math.random() > 0.8 ? 'reserved' : 'available',
      price: {
        adult: basePrice,
        child: Math.round(basePrice * 0.7),
      },
    });
  }
  return seats;
};

const generateRows = (
  startRow: number,
  endRow: number,
  seatsPerRow: number,
  sectionType: string
): Row[] => {
  const rows: Row[] = [];
  for (let i = startRow; i <= endRow; i++) {
    rows.push({
      id: `row-${i}`,
      number: i,
      seats: generateSeats(i, seatsPerRow, sectionType),
    });
  }
  return rows;
};

// Mock Seat Maps
export const mockSeatMaps: SeatMap[] = [
  {
    id: 'seatmap-1',
    vesselId: 'vessel-1',
    decks: [
      {
        id: 'deck-1-1',
        name: 'Tầng 1',
        level: 1,
        sections: [
          {
            id: 'section-1-1-1',
            name: 'Khu A - Phổ thông',
            type: 'economy',
            rows: generateRows(1, 15, 4, 'economy'),
          },
          {
            id: 'section-1-1-2',
            name: 'Khu B - Thương gia',
            type: 'business',
            rows: generateRows(16, 25, 4, 'business'),
          },
        ],
      },
      {
        id: 'deck-1-2',
        name: 'Tầng 2',
        level: 2,
        sections: [
          {
            id: 'section-1-2-1',
            name: 'Khu VIP',
            type: 'vip',
            rows: generateRows(1, 10, 3, 'vip'),
          },
        ],
      },
    ],
  },
];

// Mock Schedules
export const mockSchedules: Schedule[] = [
  {
    id: 'schedule-1',
    routeId: 'route-1',
    vesselId: 'vessel-1',
    departureTime: '08:00',
    arrivalTime: '12:00',
    date: '2025-09-17',
    basePrice: {
      adult: 200000,
      child: 140000,
    },
    status: 'active',
  },
  {
    id: 'schedule-2',
    routeId: 'route-1',
    vesselId: 'vessel-2',
    departureTime: '14:00',
    arrivalTime: '18:00',
    date: '2025-09-18',
    basePrice: {
      adult: 180000,
      child: 126000,
    },
    status: 'active',
  },
  {
    id: 'schedule-3',
    routeId: 'route-2',
    vesselId: 'vessel-1',
    departureTime: '09:00',
    arrivalTime: '13:00',
    date: '2024-12-16',
    basePrice: {
      adult: 200000,
      child: 140000,
    },
    status: 'active',
  },
  {
    id: 'schedule-4',
    routeId: 'route-5',
    vesselId: 'vessel-3',
    departureTime: '10:00',
    arrivalTime: '11:30',
    date: '2024-12-15',
    basePrice: {
      adult: 150000,
      child: 105000,
    },
    status: 'active',
  },
];

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Nguyễn Văn A',
    phone: '0901234567',
    dateOfBirth: '1990-05-15',
    createdAt: '2024-11-01T00:00:00Z',
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    name: 'Trần Thị B',
    phone: '0907654321',
    dateOfBirth: '1985-08-22',
    createdAt: '2024-11-05T00:00:00Z',
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    name: 'Lê Văn C',
    phone: '0912345678',
    dateOfBirth: '1992-03-10',
    createdAt: '2024-10-15T00:00:00Z',
  },
  {
    id: 'user-4',
    email: '<EMAIL>',
    name: 'Phạm Thị D',
    phone: '0923456789',
    dateOfBirth: '1988-12-05',
    createdAt: '2024-09-20T00:00:00Z',
  },
  {
    id: 'user-5',
    email: '<EMAIL>',
    name: 'Hoàng Văn E',
    phone: '0934567890',
    dateOfBirth: '1995-07-18',
    createdAt: '2024-11-10T00:00:00Z',
  },
  {
    id: 'user-6',
    email: '<EMAIL>',
    name: 'Võ Thị F',
    phone: '0945678901',
    dateOfBirth: '1987-09-25',
    createdAt: '2024-08-30T00:00:00Z',
  },
  {
    id: 'user-7',
    email: '<EMAIL>',
    name: 'Đặng Văn G',
    phone: '0956789012',
    dateOfBirth: '1993-11-12',
    createdAt: '2024-10-05T00:00:00Z',
  },
  {
    id: 'user-8',
    email: '<EMAIL>',
    name: 'Bùi Thị H',
    phone: '0967890123',
    dateOfBirth: '1991-04-30',
    createdAt: '2024-11-15T00:00:00Z',
  },
];

// Mock Bookings
export const mockBookings: Booking[] = [
  {
    id: 'booking-1',
    code: 'FT240001',
    userId: 'user-1',
    tripType: 'one-way',
    outboundScheduleId: 'schedule-1',
    passengers: [
      {
        id: 'passenger-1',
        name: 'Nguyễn Văn A',
        age: 34,
        type: 'adult',
        seatId: 'seat-1A',
      },
      {
        id: 'passenger-2',
        name: 'Nguyễn Thị C',
        age: 8,
        type: 'child',
        seatId: 'seat-1B',
      },
    ],
    totalAmount: 340000,
    discountAmount: 0,
    status: 'confirmed',
    paymentMethod: 'card',
    createdAt: '2024-12-10T10:30:00Z',
    updatedAt: '2024-12-10T10:35:00Z',
  },
];

// Helper functions
export const getPortById = (id: string): Port | undefined => {
  return mockPorts.find((port) => port.id === id);
};

// Removed getRouteById (routes are fetched from API)

export const getVesselById = (id: string): Vessel | undefined => {
  return mockVessels.find((vessel) => vessel.id === id);
};

export const getSeatMapById = (id: string): SeatMap | undefined => {
  return mockSeatMaps.find((seatMap) => seatMap.id === id);
};

export const getScheduleById = (id: string): Schedule | undefined => {
  return mockSchedules.find((schedule) => schedule.id === id);
};

// Removed promotions mock and helpers; use real API instead

export const getUserById = (id: string): User | undefined => {
  return mockUsers.find((user) => user.id === id);
};

export const getBookingCountByUser = (userId: string): number => {
  return mockBookings.filter((booking) => booking.userId === userId).length;
};

export const getTotalSpentByUser = (userId: string): number => {
  return mockBookings
    .filter((booking) => booking.userId === userId && booking.status === 'confirmed')
    .reduce((total, booking) => total + booking.totalAmount, 0);
};
