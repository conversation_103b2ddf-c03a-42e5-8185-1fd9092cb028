'use client';

import { Footer } from '@/components/layout/footer';
import { ShinyButton } from '@/components/magicui/shiny-button';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FerryService } from '@/lib/ferry-service';
import {
  getPortById,
  getRouteById,
  getScheduleById,
  getSeatMapById,
  getVesselById,
} from '@/lib/mock-data';
import type { Port, Route, Schedule, Vessel } from '@/lib/types';
import { ArrowLeft, User } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

interface PassengerInfo {
  name: string;
  age: string;
  type: 'adult' | 'child';
  seatNumber: string;
}

export default function PassengersPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [route, setRoute] = useState<Route | null>(null);
  const [vessel, setVessel] = useState<Vessel | null>(null);
  const [fromPort, setFromPort] = useState<Port | null>(null);
  const [toPort, setToPort] = useState<Port | null>(null);
  const [passengers, setPassengers] = useState<PassengerInfo[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Parse URL parameters
  const scheduleId = searchParams.get('scheduleId') || '';
  const seatsParam = searchParams.get('seats') || '';
  const searchQuery = {
    fromPortId: searchParams.get('from') || '',
    toPortId: searchParams.get('to') || '',
    departureDate: searchParams.get('departure') || '',
    returnDate: searchParams.get('return') || '',
    tripType: (searchParams.get('type') as 'one-way' | 'round-trip') || 'one-way',
    adultCount: Number.parseInt(searchParams.get('adults') || '1'),
    childCount: Number.parseInt(searchParams.get('children') || '0'),
  };

  const subtotal = Number.parseFloat(searchParams.get('subtotal') || '0');
  const discount = Number.parseFloat(searchParams.get('discount') || '0');
  const total = Number.parseFloat(searchParams.get('total') || '0');
  const promoCode = searchParams.get('promoCode') || '';

  useEffect(() => {
    const scheduleData = getScheduleById(scheduleId);
    if (scheduleData) {
      const routeData = getRouteById(scheduleData.routeId);
      const vesselData = getVesselById(scheduleData.vesselId);
      const fromPortData = getPortById(routeData?.fromPortId || '');
      const toPortData = getPortById(routeData?.toPortId || '');

      setSchedule(scheduleData);
      setRoute(routeData || null);
      setVessel(vesselData || null);
      setFromPort(fromPortData || null);
      setToPort(toPortData || null);

      // Initialize passenger forms
      if (seatsParam && vesselData) {
        const seatMap = getSeatMapById(vesselData.seatMapId);
        if (seatMap) {
          const passengerList: PassengerInfo[] = [];
          const seatPairs = seatsParam.split(',');

          seatPairs.forEach((pair) => {
            const [seatId, passengerType] = pair.split(':');

            // Find seat number
            let seatNumber = '';
            seatMap.decks.forEach((deck) => {
              deck.sections.forEach((section) => {
                section.rows.forEach((row) => {
                  const seat = row.seats.find((s) => s.id === seatId);
                  if (seat) {
                    seatNumber = seat.number;
                  }
                });
              });
            });

            passengerList.push({
              name: '',
              age: '',
              type: passengerType as 'adult' | 'child',
              seatNumber,
            });
          });

          setPassengers(passengerList);
        }
      }
    }
    setIsLoading(false);
  }, [scheduleId, seatsParam]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    passengers.forEach((passenger, index) => {
      if (!passenger.name.trim()) {
        newErrors[`name-${index}`] = 'Vui lòng nhập họ tên';
      }
      if (!passenger.age) {
        newErrors[`age-${index}`] = 'Vui lòng nhập tuổi';
      } else {
        const age = Number.parseInt(passenger.age);
        if (passenger.type === 'adult' && age < 12) {
          newErrors[`age-${index}`] = 'Người lớn phải từ 12 tuổi trở lên';
        }
        if (passenger.type === 'child' && (age < 2 || age >= 12)) {
          newErrors[`age-${index}`] = 'Trẻ em từ 2-11 tuổi';
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePassengerChange = (index: number, field: keyof PassengerInfo, value: string) => {
    const updatedPassengers = [...passengers];
    updatedPassengers[index] = { ...updatedPassengers[index], [field]: value };
    setPassengers(updatedPassengers);

    // Clear error for this field
    const errorKey = `${field}-${index}`;
    if (errors[errorKey]) {
      setErrors({ ...errors, [errorKey]: '' });
    }
  };

  const handleContinue = () => {
    if (!validateForm()) return;

    const paymentParams = new URLSearchParams({
      scheduleId,
      seats: seatsParam,
      passengers: JSON.stringify(passengers),
      from: searchQuery.fromPortId,
      to: searchQuery.toPortId,
      departure: searchQuery.departureDate,
      adults: searchQuery.adultCount.toString(),
      children: searchQuery.childCount.toString(),
      type: searchQuery.tripType,
      subtotal: subtotal.toString(),
      discount: discount.toString(),
      total: total.toString(),
      ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
      ...(promoCode && { promoCode }),
    });

    router.push(`/checkout/payment?${paymentParams.toString()}`);
  };

  const backUrl = `/cart?${new URLSearchParams({
    scheduleId,
    seats: seatsParam,
    from: searchQuery.fromPortId,
    to: searchQuery.toPortId,
    departure: searchQuery.departureDate,
    adults: searchQuery.adultCount.toString(),
    children: searchQuery.childCount.toString(),
    type: searchQuery.tripType,
    ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
  }).toString()}`;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />

      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          {/* Back Button */}
          <Link href={backUrl}>
            <Button variant="ghost" className="mb-6">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại giỏ hàng
            </Button>
          </Link>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <span className="ml-2 text-sm text-gray-600">Chọn ghế</span>
              </div>
              <div className="w-8 h-px bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <span className="ml-2 text-sm font-medium">Thông tin hành khách</span>
              </div>
              <div className="w-8 h-px bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <span className="ml-2 text-sm text-gray-600">Thanh toán</span>
              </div>
            </div>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Thông tin hành khách
          </h1>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Passenger Forms */}
            <div className="lg:col-span-2 space-y-6">
              {passengers.map((passenger, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>
                        Hành khách {index + 1} - Ghế {passenger.seatNumber}
                      </span>
                      <span className="text-sm font-normal text-gray-600">
                        ({passenger.type === 'adult' ? 'Người lớn' : 'Trẻ em'})
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor={`name-${index}`}>Họ và tên *</Label>
                      <Input
                        id={`name-${index}`}
                        placeholder="Nhập họ và tên"
                        value={passenger.name}
                        onChange={(e) => handlePassengerChange(index, 'name', e.target.value)}
                        className={errors[`name-${index}`] ? 'mt-2 border-red-500' : 'mt-2'}
                      />
                      {errors[`name-${index}`] && (
                        <p className="text-sm text-red-600 mt-2">{errors[`name-${index}`]}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`age-${index}`}>Tuổi *</Label>
                      <Select
                        value={passenger.age}
                        onValueChange={(value) => handlePassengerChange(index, 'age', value)}
                      >
                        <SelectTrigger
                          className={errors[`age-${index}`] ? 'mt-2 border-red-500' : 'mt-2'}
                        >
                          <SelectValue placeholder="Chọn tuổi" />
                        </SelectTrigger>
                        <SelectContent>
                          {passenger.type === 'adult'
                            ? Array.from({ length: 70 }, (_, i) => i + 12).map((age) => (
                                <SelectItem key={age} value={age.toString()}>
                                  {age} tuổi
                                </SelectItem>
                              ))
                            : Array.from({ length: 10 }, (_, i) => i + 2).map((age) => (
                                <SelectItem key={age} value={age.toString()}>
                                  {age} tuổi
                                </SelectItem>
                              ))}
                        </SelectContent>
                      </Select>
                      {errors[`age-${index}`] && (
                        <p className="text-sm text-red-600 mt-2">{errors[`age-${index}`]}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle>Tóm tắt đơn hàng</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Tạm tính</span>
                      <span>{FerryService.formatCurrency(subtotal)}</span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Giảm giá {promoCode && `(${promoCode})`}</span>
                        <span>-{FerryService.formatCurrency(discount)}</span>
                      </div>
                    )}
                    <div className="border-t pt-2 flex justify-between font-semibold">
                      <span>Tổng cộng</span>
                      <span className="text-blue-600">{FerryService.formatCurrency(total)}</span>
                    </div>
                  </div>

                  <ShinyButton
                    onClick={handleContinue}
                    className="w-full font-medium bg-blue-600 hover:bg-blue-700"
                  >
                    <span className="flex items-center justify-center text-white py-1">
                      Tiếp tục thanh toán
                    </span>
                  </ShinyButton>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
