"use client";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ArrowRight } from "lucide-react";
import { useState } from "react";

interface BookingDialogProps {
  tripTitle?: string;
  tripPrice?: string;
  buttonText?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "destructive" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  image?: string;
}

function BookingDialog({ 
  tripTitle = "Chuyến đi", 
  tripPrice = "180,000đ",
  buttonText = "Đặt vé ngay",
  variant = "default",
  size = "lg",
  image = "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=240&fit=crop"
}: BookingDialogProps) {
  const [step, setStep] = useState(1);

  const stepContent = [
    {
      title: `Tiến hành đặt vé - ${tripTitle}`,
      description: `Bạn sẽ đặt vé cho tuyến ${tripTitle} với giá từ ${tripPrice}. Vui lòng xác nhận thông tin.`,
    },
    {
      title: "Thông tin hành khách",
      description: "Vui lòng điền đầy đủ thông tin hành khách để hoàn tất việc đặt vé.",
    },
    {
      title: "Thanh toán",
      description: "Chọn phương thức thanh toán phù hợp để hoàn tất đặt vé.",
    },
    {
      title: "Hoàn thành",
      description: "Đặt vé thành công! Chúng tôi đã gửi thông tin chi tiết vào email của bạn.",
    },
  ];

  const totalSteps = stepContent.length;

  const handleContinue = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  return (
    <Dialog
      onOpenChange={(open) => {
        if (open) setStep(1);
      }}
    >
      <DialogTrigger asChild>
        <Button variant={variant} size={size}>{buttonText}</Button>
      </DialogTrigger>
      <DialogContent className="gap-0 p-0 [&>button:last-child]:text-white">
        <div className="p-2">
          <img
            className="w-full rounded-lg"
            src={image}
            width={382}
            height={216}
            alt={`Booking process for ${tripTitle}`}
          />
        </div>
        <div className="space-y-6 px-6 pb-6 pt-3">
          <DialogHeader>
            <DialogTitle>{stepContent[step - 1].title}</DialogTitle>
            <DialogDescription>{stepContent[step - 1].description}</DialogDescription>
          </DialogHeader>
          <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
            <div className="flex justify-center space-x-1.5 max-sm:order-1">
              {[...Array(totalSteps)].map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "h-1.5 w-1.5 rounded-full bg-primary",
                    index + 1 === step ? "bg-primary" : "opacity-20",
                  )}
                />
              ))}
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost">
                  {step === totalSteps ? "Đóng" : "Hủy"}
                </Button>
              </DialogClose>
              {step < totalSteps ? (
                <Button className="group" type="button" onClick={handleContinue}>
                  {step === 1 ? "Đặt vé" : "Tiếp theo"}
                  <ArrowRight
                    className="-me-1 ms-2 opacity-60 transition-transform group-hover:translate-x-0.5"
                    size={16}
                    strokeWidth={2}
                    aria-hidden="true"
                  />
                </Button>
              ) : (
                <DialogClose asChild>
                  <Button type="button">Xem vé đã đặt</Button>
                </DialogClose>
              )}
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export { BookingDialog };