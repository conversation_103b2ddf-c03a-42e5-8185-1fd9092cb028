import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const apiBaseUrl = process.env.API_URL;
    if (!apiBaseUrl) {
      return NextResponse.json(
        { success: false, error: 'Server misconfiguration: API_URL is not set' },
        { status: 500 }
      );
    }

    const body = await request.json();

    const response = await fetch(`${apiBaseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    // Try to parse JSON; if it fails, forward text
    let data: unknown;
    const text = await response.text();
    try {
      data = text ? JSON.parse(text) : {};
    } catch {
      data = { message: text };
    }

    return NextResponse.json(data as any, { status: response.status });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Unable to reach registration service' },
      { status: 502 }
    );
  }
}
