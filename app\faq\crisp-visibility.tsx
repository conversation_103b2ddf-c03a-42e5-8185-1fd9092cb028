"use client";

import { useEffect } from "react";

export default function CrispVisibility() {
  useEffect(() => {
    // Hiện widget khi vào /faq
    if (typeof window !== "undefined") {
      window.$crisp = window.$crisp || [];
      window.$crisp.push(["do", "chat:show"]);
    }

    // Ẩn widget khi rời khỏi /faq
    return () => {
      if (typeof window !== "undefined" && window.$crisp) {
        window.$crisp.push(["do", "chat:hide"]);
      }
    };
  }, []);

  return null;
}
