'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { FerryService } from '@/lib/ferry-service';
import type { SearchParams, TripSearchResult } from '@/lib/types';
import { Clock, Ship, Users } from 'lucide-react';
import Link from 'next/link';

interface TripCardProps {
  result: TripSearchResult;
  searchParams: SearchParams;
}

export function TripCard({ result, searchParams }: TripCardProps) {
  const { schedule, route, vessel, fromPort, toPort, availableSeats, minPrice } = result;

  const duration = FerryService.formatDuration(route.estimatedDuration);
  const adultPrice = FerryService.formatCurrency(minPrice.adult);
  const childPrice = FerryService.formatCurrency(minPrice.child);

  const totalPrice =
    minPrice.adult * searchParams.adultCount + minPrice.child * searchParams.childCount;

  return (
    <Card className="hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-out">
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          {/* Time & Route Info */}
          <div className="md:col-span-2">
            <div className="flex items-center justify-between mb-2">
              <div className="text-2xl font-bold text-gray-900 transition-colors hover:text-blue-600 duration-200">
                {schedule.departureTime}
              </div>
              <div className="flex items-center text-gray-500 mx-4">
                <div className="flex-1 border-t border-gray-300"></div>
                <Clock className="h-4 w-4 mx-2 animate-pulse" />
                <div className="flex-1 border-t border-gray-300"></div>
              </div>
              <div className="text-2xl font-bold text-gray-900 transition-colors hover:text-blue-600 duration-200">
                {schedule.arrivalTime}
              </div>
            </div>

            <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
              <span className="transition-transform hover:scale-105 duration-200">
                {fromPort.code}
              </span>
              <span className="font-medium text-blue-600">{duration}</span>
              <span className="transition-transform hover:scale-105 duration-200">
                {toPort.code}
              </span>
            </div>

            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center group cursor-pointer">
                <Ship className="h-4 w-4 mr-1 transition-transform group-hover:scale-110 group-hover:text-blue-600 duration-200" />
                <span className="group-hover:text-blue-600 transition-colors duration-200">
                  {vessel.name}
                </span>
              </div>
              <div className="flex items-center group cursor-pointer">
                <Users className="h-4 w-4 mr-1 transition-transform group-hover:scale-110 group-hover:text-green-600 duration-200" />
                <span className="group-hover:text-green-600 transition-colors duration-200">
                  {availableSeats} chỗ trống
                </span>
              </div>
            </div>

            {vessel.amenities.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {vessel.amenities.slice(0, 3).map((amenity) => (
                  <Badge
                    key={amenity}
                    variant="secondary"
                    className="text-xs hover:scale-105 transition-transform duration-200"
                  >
                    {amenity}
                  </Badge>
                ))}
                {vessel.amenities.length > 3 && (
                  <Badge
                    variant="secondary"
                    className="text-xs hover:scale-105 transition-transform duration-200 animate-pulse"
                  >
                    +{vessel.amenities.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Pricing */}
          <div className="text-center group">
            <div className="text-sm text-gray-600 mb-1">Từ</div>
            <div className="text-2xl font-bold text-blue-600 mb-1 transition-all duration-300 group-hover:scale-110 group-hover:text-blue-700">
              {adultPrice}
            </div>
            <div className="text-xs text-gray-500">Người lớn • Một chiều</div>
            {searchParams.childCount > 0 && (
              <div className="text-sm text-gray-600 mt-1 transition-opacity duration-200 group-hover:opacity-75">
                Trẻ em: {childPrice}
              </div>
            )}
            <div className="text-sm font-semibold text-gray-900 mt-2 pt-2 border-t transition-colors duration-300 group-hover:text-blue-600">
              Tổng: {FerryService.formatCurrency(totalPrice)}
            </div>
          </div>

          {/* Action Button */}
          <div className="text-center">
            <Link
              href={`/trip/${schedule.id}?${new URLSearchParams({
                from: searchParams.fromPortId,
                to: searchParams.toPortId,
                departure: searchParams.departureDate,
                adults: searchParams.adultCount.toString(),
                children: searchParams.childCount.toString(),
                type: searchParams.tripType,
                ...(searchParams.returnDate && { return: searchParams.returnDate }),
              }).toString()}`}
            >
              <Button className="w-full bg-blue-600 hover:bg-blue-700 transition-all duration-300 hover:scale-105 active:scale-95 hover:shadow-lg">
                Chọn chuyến
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
