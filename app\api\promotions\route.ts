import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const apiBaseUrl = process.env.API_URL;
    if (!apiBaseUrl) {
      return NextResponse.json(
        { ok: false, error: 'Server misconfiguration: API_URL is not set' },
        { status: 500 }
      );
    }

    const url = new URL(request.url);
    const qs = url.search ? url.search : '';

    const response = await fetch(`${apiBaseUrl}/api/promotions${qs}`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      // revalidate frequently if backend supports caching
      cache: 'no-store',
    });

    const text = await response.text();
    let data: unknown;
    try {
      data = text ? JSON.parse(text) : {};
    } catch {
      data = { message: text };
    }

    return NextResponse.json(data as any, { status: response.status });
  } catch (error) {
    return NextResponse.json({ ok: false, error: 'Unable to fetch promotions' }, { status: 502 });
  }
}

export async function POST(request: Request) {
  try {
    const apiBaseUrl = process.env.API_URL;
    if (!apiBaseUrl) {
      return NextResponse.json(
        { ok: false, error: 'Server misconfiguration: API_URL is not set' },
        { status: 500 }
      );
    }

    const authHeader = request.headers.get('authorization') || request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { ok: false, error: 'Missing Authorization header' },
        { status: 401 }
      );
    }

    const bodyText = await request.text();

    const response = await fetch(`${apiBaseUrl}/api/promotions`, {
      method: 'POST',
      headers: {
        Authorization: authHeader,
        'Content-Type': 'application/json',
      },
      body: bodyText,
    });

    const text = await response.text();
    let data: unknown;
    try {
      data = text ? JSON.parse(text) : {};
    } catch {
      data = { message: text };
    }

    return NextResponse.json(data as any, { status: response.status });
  } catch (error) {
    return NextResponse.json({ ok: false, error: 'Unable to create promotion' }, { status: 502 });
  }
}
