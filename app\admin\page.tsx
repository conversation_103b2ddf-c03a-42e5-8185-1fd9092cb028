"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Ship, Calendar, DollarSign, TrendingUp, Clock, FileText, Tag, BarChart3 } from "lucide-react"
import { mockBookings, mockSchedules, mockVessels, mockUsers } from "@/lib/mock-data"
import { FerryService } from "@/lib/ferry-service"

export default function AdminDashboard() {
  // Calculate statistics
  const totalBookings = mockBookings.length
  const totalRevenue = mockBookings.reduce((sum, booking) => sum + booking.totalAmount, 0)
  const totalVessels = mockVessels.length
  const totalCustomers = mockUsers.length
  const activeSchedules = mockSchedules.filter((s) => s.status === "active").length

  // Recent bookings
  const recentBookings = mockBookings.slice(0, 5)

  // Today's schedules
  const todaySchedules = mockSchedules.filter((s) => s.date === "2024-12-15").slice(0, 5)

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Tổng quan hệ thống đặt vé tàu thủy</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng đơn đặt vé</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBookings}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% so với tháng trước
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Doanh thu</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{FerryService.formatCurrency(totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +8% so với tháng trước
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khách hàng</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +15% so với tháng trước
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tàu hoạt động</CardTitle>
            <Ship className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalVessels}</div>
            <p className="text-xs text-muted-foreground">{activeSchedules} chuyến hôm nay</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <Card>
          <CardHeader>
            <CardTitle>Đơn đặt vé gần đây</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{booking.code}</p>
                    <p className="text-sm text-gray-600">{booking.passengers.length} hành khách</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{FerryService.formatCurrency(booking.totalAmount)}</p>
                    <Badge
                      variant={
                        booking.status === "confirmed"
                          ? "default"
                          : booking.status === "pending"
                            ? "secondary"
                            : "destructive"
                      }
                      className="text-xs"
                    >
                      {booking.status === "confirmed" && "Đã xác nhận"}
                      {booking.status === "pending" && "Chờ xử lý"}
                      {booking.status === "cancelled" && "Đã hủy"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Today's Schedules */}
        <Card>
          <CardHeader>
            <CardTitle>Lịch trình hôm nay</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {todaySchedules.map((schedule) => (
                <div key={schedule.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <Clock className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{schedule.departureTime}</p>
                      <p className="text-sm text-gray-600">{schedule.routeId}</p>
                    </div>
                  </div>
                  <Badge variant={schedule.status === "active" ? "default" : "secondary"} className="text-xs">
                    {schedule.status === "active" && "Hoạt động"}
                    {schedule.status === "cancelled" && "Đã hủy"}
                    {schedule.status === "full" && "Hết chỗ"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Hành động nhanh</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
              <Calendar className="h-8 w-8 text-blue-600 mb-2" />
              <h3 className="font-medium">Tạo lịch trình mới</h3>
              <p className="text-sm text-gray-600">Thêm chuyến tàu mới</p>
            </div>
            <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
              <Tag className="h-8 w-8 text-green-600 mb-2" />
              <h3 className="font-medium">Tạo khuyến mãi</h3>
              <p className="text-sm text-gray-600">Thêm mã giảm giá</p>
            </div>
            <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
              <BarChart3 className="h-8 w-8 text-purple-600 mb-2" />
              <h3 className="font-medium">Xem báo cáo</h3>
              <p className="text-sm text-gray-600">Phân tích doanh thu</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
