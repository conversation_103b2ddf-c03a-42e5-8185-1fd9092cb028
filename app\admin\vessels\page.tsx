"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Search, Edit, Eye, Ship, Users } from "lucide-react"
import { mockVessels, getSeatMapById } from "@/lib/mock-data"

export default function VesselsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedVessel, setSelectedVessel] = useState<any>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  const filteredVessels = mockVessels.filter(
    (vessel) =>
      vessel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vessel.code.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleViewVessel = (vessel: any) => {
    const seatMap = getSeatMapById(vessel.seatMapId)
    setSelectedVessel({ ...vessel, seatMap })
    setIsViewDialogOpen(true)
  }

  const getTotalSeats = (seatMap: any) => {
    if (!seatMap) return 0
    return seatMap.decks.reduce((total: number, deck: any) => {
      return (
        total +
        deck.sections.reduce((sectionTotal: number, section: any) => {
          return (
            sectionTotal +
            section.rows.reduce((rowTotal: number, row: any) => {
              return rowTotal + row.seats.length
            }, 0)
          )
        }, 0)
      )
    }, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quản lý Tàu</h1>
          <p className="text-gray-600">Quản lý thông tin tàu và sơ đồ ghế</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Thêm tàu mới
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Thêm tàu mới</DialogTitle>
              <DialogDescription>Nhập thông tin tàu mới</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="vessel-name">Tên tàu</Label>
                <Input id="vessel-name" placeholder="VD: Sài Gòn Express" />
              </div>
              <div>
                <Label htmlFor="vessel-code">Mã tàu</Label>
                <Input id="vessel-code" placeholder="VD: SGE-001" />
              </div>
              <div>
                <Label htmlFor="vessel-capacity">Sức chứa</Label>
                <Input id="vessel-capacity" type="number" placeholder="200" />
              </div>
              <div>
                <Label htmlFor="vessel-amenities">Tiện ích</Label>
                <Textarea id="vessel-amenities" placeholder="WiFi, Điều hòa, Quầy bar..." />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={() => setIsAddDialogOpen(false)}>Thêm tàu</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng số tàu</CardTitle>
            <Ship className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockVessels.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tàu hoạt động</CardTitle>
            <Ship className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockVessels.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng sức chứa</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockVessels.reduce((total, vessel) => total + vessel.capacity, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sức chứa TB</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(mockVessels.reduce((total, vessel) => total + vessel.capacity, 0) / mockVessels.length)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách tàu</CardTitle>
          <CardDescription>Quản lý thông tin các tàu trong hệ thống</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm theo tên tàu hoặc mã tàu..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên tàu</TableHead>
                <TableHead>Mã tàu</TableHead>
                <TableHead>Sức chứa</TableHead>
                <TableHead>Tiện ích</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVessels.map((vessel) => (
                <TableRow key={vessel.id}>
                  <TableCell className="font-medium">{vessel.name}</TableCell>
                  <TableCell>{vessel.code}</TableCell>
                  <TableCell>{vessel.capacity} ghế</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {vessel.amenities.slice(0, 2).map((amenity) => (
                        <Badge key={amenity} variant="secondary" className="text-xs">
                          {amenity}
                        </Badge>
                      ))}
                      {vessel.amenities.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{vessel.amenities.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Hoạt động
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleViewVessel(vessel)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* View Vessel Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Chi tiết tàu: {selectedVessel?.name}</DialogTitle>
            <DialogDescription>Thông tin chi tiết và sơ đồ ghế</DialogDescription>
          </DialogHeader>
          {selectedVessel && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tên tàu</Label>
                  <p className="text-sm text-gray-600">{selectedVessel.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Mã tàu</Label>
                  <p className="text-sm text-gray-600">{selectedVessel.code}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Sức chứa</Label>
                  <p className="text-sm text-gray-600">{selectedVessel.capacity} ghế</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Tổng ghế thực tế</Label>
                  <p className="text-sm text-gray-600">
                    {selectedVessel.seatMap ? getTotalSeats(selectedVessel.seatMap) : 0} ghế
                  </p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Tiện ích</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedVessel.amenities.map((amenity: string) => (
                    <Badge key={amenity} variant="secondary">
                      {amenity}
                    </Badge>
                  ))}
                </div>
              </div>

              {selectedVessel.seatMap && (
                <div>
                  <Label className="text-sm font-medium">Sơ đồ ghế</Label>
                  <div className="mt-2 space-y-4">
                    {selectedVessel.seatMap.decks.map((deck: any) => (
                      <Card key={deck.id}>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">{deck.name}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {deck.sections.map((section: any) => (
                              <div key={section.id}>
                                <h4 className="font-medium text-sm mb-2">{section.name}</h4>
                                <div className="text-xs text-gray-600">
                                  {section.rows.length} hàng × {section.rows[0]?.seats.length || 0} ghế ={" "}
                                  {section.rows.length * (section.rows[0]?.seats.length || 0)} ghế
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Đóng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
