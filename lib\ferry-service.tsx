import { getPortById, getSeatMapById, getVesselById, mockRoutes, mockSchedules } from './mock-data';
import type { SearchParams, TripSearchResult } from './types';

// Ocean Pass service functions
export class FerryService {
  static searchTrips(params: SearchParams): TripSearchResult[] {
    const { fromPortId, toPortId, departureDate, adultCount, childCount } = params;

    // Find matching routes
    const matchingRoutes = mockRoutes.filter(
      (route) => route.fromPortId === fromPortId && route.toPortId === toPortId
    );

    if (matchingRoutes.length === 0) {
      return [];
    }

    // Find schedules for the routes on the specified date
    const results: TripSearchResult[] = [];

    for (const route of matchingRoutes) {
      const schedules = mockSchedules.filter(
        (schedule) =>
          schedule.routeId === route.id &&
          schedule.date === departureDate &&
          schedule.status === 'active'
      );

      for (const schedule of schedules) {
        const vessel = getVesselById(schedule.vesselId);
        const fromPort = getPortById(route.fromPortId);
        const toPort = getPortById(route.toPortId);
        const seatMap = getSeatMapById(vessel?.seatMapId || '');

        if (vessel && fromPort && toPort && seatMap) {
          // Calculate available seats
          const totalSeats = seatMap.decks.reduce((total, deck) => {
            return (
              total +
              deck.sections.reduce((sectionTotal, section) => {
                return (
                  sectionTotal +
                  section.rows.reduce((rowTotal, row) => {
                    return (
                      rowTotal + row.seats.filter((seat) => seat.status === 'available').length
                    );
                  }, 0)
                );
              }, 0)
            );
          }, 0);

          // Find minimum price
          let minAdultPrice = Number.POSITIVE_INFINITY;
          let minChildPrice = Number.POSITIVE_INFINITY;

          seatMap.decks.forEach((deck) => {
            deck.sections.forEach((section) => {
              section.rows.forEach((row) => {
                row.seats.forEach((seat) => {
                  if (seat.status === 'available') {
                    minAdultPrice = Math.min(minAdultPrice, seat.price.adult);
                    minChildPrice = Math.min(minChildPrice, seat.price.child);
                  }
                });
              });
            });
          });

          // Check if there are enough available seats
          const requiredSeats = adultCount + childCount;
          if (totalSeats >= requiredSeats) {
            results.push({
              schedule,
              route,
              vessel,
              fromPort,
              toPort,
              availableSeats: totalSeats,
              minPrice: {
                adult:
                  minAdultPrice === Number.POSITIVE_INFINITY
                    ? schedule.basePrice.adult
                    : minAdultPrice,
                child:
                  minChildPrice === Number.POSITIVE_INFINITY
                    ? schedule.basePrice.child
                    : minChildPrice,
              },
            });
          }
        }
      }
    }

    // Sort by departure time
    return results.sort((a, b) => a.schedule.departureTime.localeCompare(b.schedule.departureTime));
  }

  static calculateTripPrice(
    scheduleId: string,
    seatIds: string[],
    passengerTypes: ('adult' | 'child')[]
  ): number {
    const schedule = mockSchedules.find((s) => s.id === scheduleId);
    if (!schedule) return 0;

    const vessel = getVesselById(schedule.vesselId);
    if (!vessel) return 0;

    const seatMap = getSeatMapById(vessel.seatMapId);
    if (!seatMap) return 0;

    let totalPrice = 0;

    seatIds.forEach((seatId, index) => {
      const passengerType = passengerTypes[index];

      // Find the seat in the seat map
      let seatFound = false;
      seatMap.decks.forEach((deck) => {
        deck.sections.forEach((section) => {
          section.rows.forEach((row) => {
            const seat = row.seats.find((s) => s.id === seatId);
            if (seat && !seatFound) {
              totalPrice += seat.price[passengerType];
              seatFound = true;
            }
          });
        });
      });
    });

    return totalPrice;
  }

  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }

  static formatDuration(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins > 0 ? mins + 'm' : ''}`;
  }

  static generateBookingCode(): string {
    const prefix = 'FT';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 100)
      .toString()
      .padStart(2, '0');
    return `${prefix}${timestamp}${random}`;
  }

  static generateQRCode(bookingCode: string, passengerName: string): string {
    // In a real app, this would generate an actual QR code
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="white"/>
        <text x="100" y="100" text-anchor="middle" font-family="monospace" font-size="12">
          ${bookingCode}
        </text>
        <text x="100" y="120" text-anchor="middle" font-family="monospace" font-size="10">
          ${passengerName}
        </text>
      </svg>
    `)}`;
  }
}
