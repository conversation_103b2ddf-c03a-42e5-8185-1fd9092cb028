"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Search, Eye, FileText, Calendar, Users } from "lucide-react"
import { mockBookings, getPortById, getScheduleById, getRouteById } from "@/lib/mock-data"
import { FerryService } from "@/lib/ferry-service"
import type { Booking } from "@/lib/types"

export default function BookingsPage() {
  const [bookings] = useState<Booking[]>(mockBookings)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)

  const filteredBookings = bookings.filter(
    (booking) =>
      booking.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.passengers.some((p) => p.name.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  const getBookingDetails = (booking: Booking) => {
    const schedule = getScheduleById(booking.outboundScheduleId)
    const route = schedule ? getRouteById(schedule.routeId) : null
    const fromPort = route ? getPortById(route.fromPortId) : null
    const toPort = route ? getPortById(route.toPortId) : null

    return { schedule, route, fromPort, toPort }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-100 text-green-800">Đã xác nhận</Badge>
      case "pending":
        return <Badge variant="secondary">Chờ xử lý</Badge>
      case "cancelled":
        return <Badge variant="destructive">Đã hủy</Badge>
      case "completed":
        return <Badge className="bg-blue-100 text-blue-800">Hoàn thành</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Quản lý đơn đặt vé</h1>
        <p className="text-gray-600">Theo dõi và quản lý tất cả đơn đặt vé</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng đơn hàng</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookings.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Đã xác nhận</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookings.filter((b) => b.status === "confirmed").length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chờ xử lý</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookings.filter((b) => b.status === "pending").length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng doanh thu</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {FerryService.formatCurrency(bookings.reduce((sum, b) => sum + b.totalAmount, 0))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bookings Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Danh sách đơn đặt vé</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm theo mã đơn hoặc tên khách hàng..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-80"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã đơn</TableHead>
                <TableHead>Khách hàng</TableHead>
                <TableHead>Tuyến</TableHead>
                <TableHead>Ngày đi</TableHead>
                <TableHead>Hành khách</TableHead>
                <TableHead>Tổng tiền</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredBookings.map((booking) => {
                const { schedule, fromPort, toPort } = getBookingDetails(booking)
                return (
                  <TableRow key={booking.id}>
                    <TableCell className="font-medium">{booking.code}</TableCell>
                    <TableCell>
                      {booking.passengers[0]?.name || "N/A"}
                      {booking.passengers.length > 1 && (
                        <span className="text-gray-500"> +{booking.passengers.length - 1}</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {fromPort?.name} → {toPort?.name}
                    </TableCell>
                    <TableCell>{schedule?.date}</TableCell>
                    <TableCell>{booking.passengers.length}</TableCell>
                    <TableCell>{FerryService.formatCurrency(booking.totalAmount)}</TableCell>
                    <TableCell>{getStatusBadge(booking.status)}</TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedBooking(booking)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Chi tiết đơn đặt vé {booking.code}</DialogTitle>
                          </DialogHeader>
                          {selectedBooking && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-semibold">Thông tin chuyến</h4>
                                  <p>
                                    {fromPort?.name} → {toPort?.name}
                                  </p>
                                  <p>
                                    {schedule?.date} • {schedule?.departureTime}
                                  </p>
                                  <p>{selectedBooking.tripType === "round-trip" ? "Khứ hồi" : "Một chiều"}</p>
                                </div>
                                <div>
                                  <h4 className="font-semibold">Thông tin thanh toán</h4>
                                  <p>Tổng tiền: {FerryService.formatCurrency(selectedBooking.totalAmount)}</p>
                                  <p>Giảm giá: {FerryService.formatCurrency(selectedBooking.discountAmount)}</p>
                                  <p>Trạng thái: {getStatusBadge(selectedBooking.status)}</p>
                                </div>
                              </div>

                              <div>
                                <h4 className="font-semibold mb-2">Danh sách hành khách</h4>
                                <div className="space-y-2">
                                  {selectedBooking.passengers.map((passenger, index) => (
                                    <div key={index} className="flex justify-between p-2 bg-gray-50 rounded">
                                      <span>
                                        {passenger.name} ({passenger.age} tuổi)
                                      </span>
                                      <span>Ghế {passenger.seatId}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
