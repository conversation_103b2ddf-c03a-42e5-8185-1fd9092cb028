'use client';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { showErrorToast, showSuccessToast } from '@/components/ui/soft-toasts';
import { useAuth } from '@/lib/auth-context';
import { Calendar, Mail, Phone, User } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function ProfilePage() {
  const { user, setSession } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
  });
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) return;

    const fetchMe = async () => {
      try {
        const res = await fetch('/api/auth/me', {
          headers: { Authorization: `Bearer ${token}` },
        });
        const data = await res.json().catch(() => ({}));
        if (res.ok && data && (data.data || data.user)) {
          const apiUser = data.data?.user || data.data || data.user;
          const tok = data.data?.token || token; // keep current token if none returned
          setSession({ user: apiUser, token: tok });
          setFormData({ name: apiUser.name || '', phone: apiUser.phone || '' });
        } else if (!res.ok) {
          const message =
            (data && (data.message || data.error)) || 'Không tải được thông tin người dùng';
          setError(message);
          showErrorToast(message);
        }
      } catch (e) {
        const message = 'Không thể kết nối tới máy chủ.';
        setError(message);
        showErrorToast(message);
      }
    };

    fetchMe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar04Page />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <Alert>
            <AlertDescription>Vui lòng đăng nhập để xem thông tin cá nhân.</AlertDescription>
          </Alert>
        </div>
        <Footer />
      </div>
    );
  }

  const handleSave = async () => {
    setSuccess('');
    setError('');
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      if (!token) {
        const message = 'Bạn chưa đăng nhập.';
        setError(message);
        showErrorToast(message);
        return;
      }

      const res = await fetch('/api/users/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ name: formData.name, phone: formData.phone }),
      });
      const data = await res.json().catch(() => ({}));
      if (res.ok && (data.data || data.user)) {
        const updatedUser = data.data?.user || data.user || data.data; // support different shapes
        const newToken = data.data?.token; // if backend returns a refreshed token
        setSession({ user: updatedUser, token: newToken || undefined });
        setSuccess('Thông tin đã được cập nhật thành công!');
        showSuccessToast('Cập nhật thông tin thành công');
        setIsEditing(false);
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const message = (data && (data.message || data.error)) || 'Cập nhật thất bại';
        setError(message);
        showErrorToast(message);
      }
    } catch (e) {
      const message = 'Không thể kết nối tới máy chủ.';
      setError(message);
      showErrorToast(message);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900">Thông tin cá nhân</h1>
            <p className="text-gray-600 mt-2">Quản lý thông tin tài khoản của bạn</p>
          </div>

          {(success || error) && (
            <Alert className="mb-6">
              <AlertDescription>{success || error}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Thông tin cơ bản
                </CardTitle>
                <CardDescription>Thông tin cá nhân và liên hệ của bạn</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Họ và tên</Label>
                    {isEditing ? (
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                      />
                    ) : (
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <User className="h-4 w-4 text-gray-500" />
                        <span>{user.name}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{user.email}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Số điện thoại</Label>
                    {isEditing ? (
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, ''); // ngăn nhập chữ và kí tự
                          if (value.length <= 10) {
                            // Limit to 10 digits
                            setFormData((prev) => ({ ...prev, phone: value }));
                          }
                        }}
                        placeholder="Nhập số điện thoại 10 số"
                        maxLength={10}
                      />
                    ) : (
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span>{user.phone || 'Chưa cập nhật'}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Ngày tham gia</Label>
                    <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>{new Date(user.createdAt).toLocaleDateString('vi-VN')}</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap justify-center gap-2 pt-4">
                  {isEditing ? (
                    <>
                      <Button className="px-5" onClick={handleSave}>
                        Lưu thay đổi
                      </Button>
                      <Button
                        className="px-5"
                        variant="outline"
                        onClick={() => setIsEditing(false)}
                      >
                        Hủy
                      </Button>
                    </>
                  ) : (
                    <Button className="px-5" onClick={() => setIsEditing(true)}>
                      Chỉnh sửa thông tin
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Bảo mật tài khoản</CardTitle>
                <CardDescription>Quản lý mật khẩu và bảo mật tài khoản</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center">
                <Button variant="outline" className="w-40 text-center">
                  Đổi mật khẩu
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
