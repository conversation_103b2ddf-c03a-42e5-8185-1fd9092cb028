"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { SeatMap as SeatMapType, Seat, PassengerType } from "@/lib/types"
import { FerryService } from "@/lib/ferry-service"

interface SelectedSeat {
  seat: Seat
  passengerType: PassengerType
  passengerIndex: number
}

interface SeatMapProps {
  seatMap: SeatMapType
  selectedSeats: SelectedSeat[]
  onSeatSelect: (seat: Seat) => void
  totalPassengers: number
}

export function SeatMap({ seatMap, selectedSeats, onSeatSelect, totalPassengers }: SeatMapProps) {
  const isSelected = (seatId: string) => selectedSeats.some((s) => s.seat.id === seatId)

  const getSeatStatusColor = (seat: Seat) => {
    if (isSelected(seat.id)) return "bg-blue-600 text-white border-blue-600"
    if (seat.status === "booked") return "bg-red-500 text-white border-red-500"
    if (seat.status === "reserved") return "bg-yellow-500 text-white border-yellow-500"
    return "bg-white text-gray-700 border-gray-300 hover:border-blue-400 hover:bg-blue-50"
  }

  const canSelectMoreSeats = selectedSeats.length < totalPassengers

  return (
    <div className="space-y-6">
      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Chọn ghế</CardTitle>
          <p className="text-sm text-gray-600">
            Đã chọn {selectedSeats.length}/{totalPassengers} ghế
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded"></div>
              <span>Còn trống</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-600 rounded"></div>
              <span>Đã chọn</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-yellow-500 rounded"></div>
              <span>Đang giữ</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span>Đã đặt</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deck Maps */}
      {seatMap.decks.map((deck) => (
        <Card key={deck.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">{deck.name}</CardTitle>
              <Badge variant="outline">Tầng {deck.level}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {deck.sections.map((section) => (
                <div key={section.id}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold text-gray-900">{section.name}</h3>
                    <Badge
                      variant={
                        section.type === "vip" ? "default" : section.type === "business" ? "secondary" : "outline"
                      }
                    >
                      {section.type === "economy" && "Phổ thông"}
                      {section.type === "business" && "Thương gia"}
                      {section.type === "vip" && "VIP"}
                      {section.type === "cabin" && "Cabin"}
                    </Badge>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="space-y-2">
                      {section.rows.map((row) => (
                        <div key={row.id} className="flex items-center justify-center space-x-2">
                          <div className="w-8 text-center text-sm font-medium text-gray-600">{row.number}</div>
                          <div className="flex space-x-1">
                            {row.seats.slice(0, Math.ceil(row.seats.length / 2)).map((seat) => (
                              <Button
                                key={seat.id}
                                variant="outline"
                                size="sm"
                                className={cn(
                                  "w-10 h-10 p-0 text-xs font-medium transition-all",
                                  getSeatStatusColor(seat),
                                  seat.status === "available" && canSelectMoreSeats
                                    ? "cursor-pointer"
                                    : seat.status !== "available"
                                      ? "cursor-not-allowed"
                                      : "cursor-not-allowed opacity-50",
                                )}
                                onClick={() => onSeatSelect(seat)}
                                disabled={seat.status !== "available" || (!isSelected(seat.id) && !canSelectMoreSeats)}
                                title={`Ghế ${seat.number} - ${FerryService.formatCurrency(seat.price.adult)} (Người lớn) / ${FerryService.formatCurrency(seat.price.child)} (Trẻ em)`}
                              >
                                {seat.number}
                              </Button>
                            ))}
                          </div>
                          <div className="w-8 text-center text-xs text-gray-400">lối đi</div>
                          <div className="flex space-x-1">
                            {row.seats.slice(Math.ceil(row.seats.length / 2)).map((seat) => (
                              <Button
                                key={seat.id}
                                variant="outline"
                                size="sm"
                                className={cn(
                                  "w-10 h-10 p-0 text-xs font-medium transition-all",
                                  getSeatStatusColor(seat),
                                  seat.status === "available" && canSelectMoreSeats
                                    ? "cursor-pointer"
                                    : seat.status !== "available"
                                      ? "cursor-not-allowed"
                                      : "cursor-not-allowed opacity-50",
                                )}
                                onClick={() => onSeatSelect(seat)}
                                disabled={seat.status !== "available" || (!isSelected(seat.id) && !canSelectMoreSeats)}
                                title={`Ghế ${seat.number} - ${FerryService.formatCurrency(seat.price.adult)} (Người lớn) / ${FerryService.formatCurrency(seat.price.child)} (Trẻ em)`}
                              >
                                {seat.number}
                              </Button>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Price Info for Section */}
                  <div className="mt-3 text-sm text-gray-600">
                    <p>
                      Giá từ: {FerryService.formatCurrency(section.rows[0]?.seats[0]?.price.adult || 0)} (Người lớn) •{" "}
                      {FerryService.formatCurrency(section.rows[0]?.seats[0]?.price.child || 0)} (Trẻ em)
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
