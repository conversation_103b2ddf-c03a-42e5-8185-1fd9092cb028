'use client';

import CustomerReviews from '@/components/app/promotions/customer-reviews';
import PromotionCard from '@/components/app/promotions/promotion-card';
import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { showErrorToast, showSuccessToast } from '@/components/ui/soft-toasts';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { Promotion } from '@/lib/types';
import { ArrowRight, Gift, Info, Percent, PhoneOutgoing, Shield, Ship, Star } from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

export default function PromotionsPage() {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const now = new Date();

  // Fetch promotions from API
  React.useEffect(() => {
    const fetchPromotions = async () => {
      try {
        const res = await fetch('/api/promotions', { cache: 'no-store' });
        const data = await res.json().catch(() => ({}));
        if (res.ok) {
          const rawItems = (data && (data.data?.items || data.items || data.data || data)) || [];
          const itemsArray = Array.isArray(rawItems) ? rawItems : [];
          const mapped: Promotion[] = itemsArray.map((it: any) => ({
            id: String(it.id ?? it.promotion_id ?? ''),
            code: it.code ?? it.promo_code ?? '',
            name: it.name ?? it.title ?? '',
            description: it.description ?? '',
            type: (it.type === 'percentage' ? 'percentage' : 'fixed') as Promotion['type'],
            value: Number(it.value ?? it.amount ?? 0),
            minAmount:
              it.min_amount !== undefined && it.min_amount !== null
                ? Number(it.min_amount)
                : undefined,
            maxDiscount:
              it.max_discount !== undefined && it.max_discount !== null
                ? Number(it.max_discount)
                : undefined,
            validFrom: it.valid_from ?? it.start_date ?? it.validFrom ?? '',
            validTo: it.valid_to ?? it.end_date ?? it.validTo ?? '',
            isActive: Boolean(it.is_active ?? it.isActive ?? false),
          }));
          setPromotions(mapped);
        } else {
          showErrorToast((data && (data.message || data.error)) || 'Không tải được khuyến mãi');
        }
      } catch (e) {
        showErrorToast('Không thể kết nối tới máy chủ.');
      } finally {
        setLoading(false);
      }
    };
    fetchPromotions();
  }, []);

  // Utility to check if promotion is currently valid
  const isPromotionValid = (promo: Promotion) => {
    const from = new Date(promo.validFrom);
    const to = new Date(promo.validTo);
    return promo.isActive && from <= now && now <= to;
  };

  // Utility to check if promotion is expiring soon (within 7 days)
  const isExpiringSoon = (promo: Promotion, days = 7) => {
    const to = new Date(promo.validTo);
    const diff = to.getTime() - now.getTime();
    return diff > 0 && diff <= days * 24 * 60 * 60 * 1000;
  };

  const activePromotions = promotions.filter(isPromotionValid);
  const expiringPromotions = promotions.filter((p) => !isPromotionValid(p) && isExpiringSoon(p));

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const copyPromoCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      showSuccessToast(`Đã sao chép mã: ${code}`);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      showErrorToast('Sao chép mã thất bại');
    }
  };

  const getPromotionIcon = (type: string) => {
    return type === 'percentage' ? <Percent className="h-5 w-5" /> : <Gift className="h-5 w-5" />;
  };

  const getPromotionValue = (promotion: Promotion) => {
    if (promotion.type === 'percentage') {
      return `Giảm ${promotion.value}%`;
    }
    return `Giảm ${formatCurrency(promotion.value)}`;
  };

  const getPromotionCondition = (promotion: Promotion) => {
    let condition = `Đơn hàng từ ${formatCurrency(promotion.minAmount || 0)}`;
    if (promotion.type === 'percentage' && promotion.maxDiscount) {
      condition += ` • Giảm tối đa ${formatCurrency(promotion.maxDiscount)}`;
    }
    return condition;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Navbar04Page />
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-20 ">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Ưu đãi tháng 9</h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Tiết kiệm chi phí với các chương trình khuyến mãi hấp dẫn từ Ocean Pass
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Alert thông báo */}
        <Alert className="mb-8 border-amber-200 bg-amber-50">
          <Info className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            <strong>Lưu ý:</strong> Mã khuyến mãi WELCOME dành cho khách hàng mới đăng ký. Đặt vé
            ngay để không bỏ lỡ!
          </AlertDescription>
        </Alert>

        {/* Thống kê */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="py-1">
              <div className="text-2xl font-bold text-blue-600 mb-1">{activePromotions.length}</div>
              <div className="text-sm text-gray-600">Khuyến mãi hiện có</div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="py-1">
              <div className="text-2xl font-bold text-green-600 mb-1">20%</div>
              <div className="text-sm text-gray-600">Giảm tối đa</div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="py-1">
              <div className="text-2xl font-bold text-purple-600 mb-1">5K+</div>
              <div className="text-sm text-gray-600">Khách đã sử dụng</div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="py-1">
              <div className="text-2xl font-bold text-orange-600 mb-1">4.8★</div>
              <div className="text-sm text-gray-600">Đánh giá trung bình</div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs cho các loại khuyến mãi */}
        <Tabs defaultValue="all" className="mb-12">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">Tất cả</TabsTrigger>
            <TabsTrigger value="percentage">Giảm theo %</TabsTrigger>
            <TabsTrigger value="fixed">Giảm cố định</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                ...activePromotions,
                // include expiring promotions (they might not be active flag-wise but are near expiry)
                ...expiringPromotions,
              ].map((promotion) => (
                <PromotionCard
                  key={promotion.id}
                  promotion={promotion}
                  copiedCode={copiedCode}
                  onCopyCode={copyPromoCode}
                  isExpiring={isExpiringSoon(promotion)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="percentage" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activePromotions
                .filter((p) => p.type === 'percentage')
                .map((promotion) => (
                  <PromotionCard
                    key={promotion.id}
                    promotion={promotion}
                    copiedCode={copiedCode}
                    onCopyCode={copyPromoCode}
                    isExpiring={isExpiringSoon(promotion)}
                  />
                ))}
            </div>
          </TabsContent>

          <TabsContent value="fixed" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activePromotions
                .filter((p) => p.type === 'fixed')
                .map((promotion) => (
                  <PromotionCard
                    key={promotion.id}
                    promotion={promotion}
                    copiedCode={copiedCode}
                    onCopyCode={copyPromoCode}
                    isExpiring={isExpiringSoon(promotion)}
                  />
                ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Đánh giá khách hàng */}
        <CustomerReviews />

        {/* CTA Section */}
        <Card className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white border-0">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full translate-y-24 -translate-x-24"></div>

          <CardContent className="relative py-16 px-8 text-center">
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-full mb-6">
              <Ship className="h-8 w-8 text-white" />
            </div>

            {/* Heading */}
            <h2 className="text-4xl md:text-5xl font-bold mb-4 pb-2 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Sẵn sàng đặt vé?
            </h2>

            {/* Description */}
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
              Sử dụng các mã khuyến mãi trên để tiết kiệm chi phí cho chuyến đi của bạn.
              <br className="hidden md:block" />
              Đặt vé ngay để không bỏ lỡ những ưu đãi hấp dẫn!
            </p>

            {/* Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/">
                <Button
                  size="lg"
                  className="w-48 bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-3 h-12 group transition-all duration-300 transform hover:scale-105"
                >
                  <Ship className="mr-2 h-5 w-5" />
                  Đặt vé ngay
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>

              <Link href="/faq">
                <Button
                  size="lg"
                  className="w-48 bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-3 h-12 group transition-all duration-300 transform hover:scale-105"
                >
                  <PhoneOutgoing className="mr-2 h-5 w-5" />
                  Liên hệ tư vấn
                </Button>
              </Link>
            </div>

            {/* Trust indicators */}
            <div className="mt-8 flex flex-wrap justify-center items-center gap-6 text-blue-200 text-sm">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span>4.8/5 đánh giá</span>
              </div>
              <div className="flex items-center gap-2">
                <Gift className="h-4 w-4" />
                <span>Ưu đãi độc quyền</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>Đặt vé an toàn</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  );
}
