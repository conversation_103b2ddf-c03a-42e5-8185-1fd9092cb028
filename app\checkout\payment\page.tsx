'use client';

import { Footer } from '@/components/layout/footer';
import { ShinyButton } from '@/components/magicui/shiny-button';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { FerryService } from '@/lib/ferry-service';
import type { PaymentMethod } from '@/lib/types';
import { ArrowLeft, Building, CreditCard, Lock, Smartphone } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

export default function PaymentPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('card');
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardName, setCardName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Parse URL parameters
  const scheduleId = searchParams.get('scheduleId') || '';
  const seatsParam = searchParams.get('seats') || '';
  const passengersParam = searchParams.get('passengers') || '';
  const searchQuery = {
    fromPortId: searchParams.get('from') || '',
    toPortId: searchParams.get('to') || '',
    departureDate: searchParams.get('departure') || '',
    returnDate: searchParams.get('return') || '',
    tripType: (searchParams.get('type') as 'one-way' | 'round-trip') || 'one-way',
    adultCount: Number.parseInt(searchParams.get('adults') || '1'),
    childCount: Number.parseInt(searchParams.get('children') || '0'),
  };

  const subtotal = Number.parseFloat(searchParams.get('subtotal') || '0');
  const discount = Number.parseFloat(searchParams.get('discount') || '0');
  const total = Number.parseFloat(searchParams.get('total') || '0');
  const promoCode = searchParams.get('promoCode') || '';

  const passengers = passengersParam ? JSON.parse(passengersParam) : [];

  const validatePayment = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (paymentMethod === 'card') {
      if (!cardNumber || cardNumber.replace(/\s/g, '').length !== 16) {
        newErrors.cardNumber = 'Số thẻ không hợp lệ';
      }
      if (!expiryDate || !/^\d{2}\/\d{2}$/.test(expiryDate)) {
        newErrors.expiryDate = 'Ngày hết hạn không hợp lệ (MM/YY)';
      }
      if (!cvv || cvv.length !== 3) {
        newErrors.cvv = 'CVV không hợp lệ';
      }
      if (!cardName.trim()) {
        newErrors.cardName = 'Vui lòng nhập tên chủ thẻ';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePayment = async () => {
    if (!validatePayment()) return;

    setIsProcessing(true);

    // Simulate payment processing
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Generate booking code
    const bookingCode = FerryService.generateBookingCode();

    // Redirect to success page
    const successParams = new URLSearchParams({
      bookingCode,
      scheduleId,
      seats: seatsParam,
      passengers: passengersParam,
      from: searchQuery.fromPortId,
      to: searchQuery.toPortId,
      departure: searchQuery.departureDate,
      adults: searchQuery.adultCount.toString(),
      children: searchQuery.childCount.toString(),
      type: searchQuery.tripType,
      subtotal: subtotal.toString(),
      discount: discount.toString(),
      total: total.toString(),
      paymentMethod,
      ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
      ...(promoCode && { promoCode }),
    });

    router.push(`/checkout/success?${successParams.toString()}`);
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }
    return v;
  };

  const backUrl = `/checkout/passengers?${new URLSearchParams({
    scheduleId,
    seats: seatsParam,
    from: searchQuery.fromPortId,
    to: searchQuery.toPortId,
    departure: searchQuery.departureDate,
    adults: searchQuery.adultCount.toString(),
    children: searchQuery.childCount.toString(),
    type: searchQuery.tripType,
    subtotal: subtotal.toString(),
    discount: discount.toString(),
    total: total.toString(),
    ...(searchQuery.returnDate && { return: searchQuery.returnDate }),
    ...(promoCode && { promoCode }),
  }).toString()}`;

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />

      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          {/* Back Button */}
          <Link href={backUrl}>
            <Button variant="ghost" className="mb-6">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại thông tin hành khách
            </Button>
          </Link>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <span className="ml-2 text-sm text-gray-600">Chọn ghế</span>
              </div>
              <div className="w-8 h-px bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  ✓
                </div>
                <span className="ml-2 text-sm text-gray-600">Thông tin hành khách</span>
              </div>
              <div className="w-8 h-px bg-gray-300"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <span className="ml-2 text-sm font-medium">Thanh toán</span>
              </div>
            </div>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">Thanh toán</h1>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Payment Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Payment Method Selection */}
              <Card>
                <CardHeader>
                  <CardTitle>Phương thức thanh toán</CardTitle>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={paymentMethod}
                    onValueChange={(value: PaymentMethod) => setPaymentMethod(value)}
                  >
                    <div className="flex items-center space-x-2 p-4 border rounded-lg">
                      <RadioGroupItem value="card" id="card" />
                      <CreditCard className="h-5 w-5 text-gray-600" />
                      <Label htmlFor="card" className="flex-1 cursor-pointer">
                        Thẻ tín dụng / Thẻ ghi nợ
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 p-4 border rounded-lg">
                      <RadioGroupItem value="e-wallet" id="e-wallet" />
                      <Smartphone className="h-5 w-5 text-gray-600" />
                      <Label htmlFor="e-wallet" className="flex-1 cursor-pointer">
                        Ví điện tử (MoMo, ZaloPay, VNPay)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 p-4 border rounded-lg">
                      <RadioGroupItem value="bank-transfer" id="bank-transfer" />
                      <Building className="h-5 w-5 text-gray-600" />
                      <Label htmlFor="bank-transfer" className="flex-1 cursor-pointer">
                        Chuyển khoản ngân hàng
                      </Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>

              {/* Payment Details */}
              {paymentMethod === 'card' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Lock className="h-5 w-5" />
                      <span>Thông tin thẻ</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="card-number">Số thẻ *</Label>
                      <Input
                        id="card-number"
                        placeholder="1234 5678 9012 3456"
                        value={cardNumber}
                        onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                        maxLength={19}
                        className={errors.cardNumber ? 'mt-2 border-red-500' : 'mt-2'}
                      />
                      {errors.cardNumber && (
                        <p className="text-sm text-red-600 mt-2">{errors.cardNumber}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="expiry-date">Ngày hết hạn *</Label>
                        <Input
                          id="expiry-date"
                          placeholder="MM/YY"
                          value={expiryDate}
                          onChange={(e) => setExpiryDate(formatExpiryDate(e.target.value))}
                          maxLength={5}
                          className={errors.expiryDate ? 'mt-2 border-red-500' : 'mt-2'}
                        />
                        {errors.expiryDate && (
                          <p className="text-sm text-red-600 mt-2">{errors.expiryDate}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="cvv">CVV *</Label>
                        <Input
                          id="cvv"
                          placeholder="123"
                          value={cvv}
                          onChange={(e) => setCvv(e.target.value.replace(/\D/g, ''))}
                          maxLength={3}
                          className={errors.cvv ? 'mt-2 border-red-500' : 'mt-2'}
                        />
                        {errors.cvv && <p className="text-sm text-red-600 mt-2">{errors.cvv}</p>}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="card-name">Tên chủ thẻ *</Label>
                      <Input
                        id="card-name"
                        placeholder="NGUYEN VAN A"
                        value={cardName}
                        onChange={(e) => setCardName(e.target.value.toUpperCase())}
                        className={errors.cardName ? 'mt-2 border-red-500' : 'mt-2'}
                      />
                      {errors.cardName && (
                        <p className="text-sm text-red-600 mt-2">{errors.cardName}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {paymentMethod === 'e-wallet' && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">
                      Bạn sẽ được chuyển đến ứng dụng ví điện tử để hoàn tất thanh toán
                    </p>
                  </CardContent>
                </Card>
              )}

              {paymentMethod === 'bank-transfer' && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">
                      Thông tin chuyển khoản sẽ được hiển thị sau khi xác nhận đơn hàng
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <CardHeader>
                  <CardTitle>Tóm tắt đơn hàng</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Hành khách</span>
                      <span>{passengers.length} người</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tạm tính</span>
                      <span>{FerryService.formatCurrency(subtotal)}</span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Giảm giá {promoCode && `(${promoCode})`}</span>
                        <span>-{FerryService.formatCurrency(discount)}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Tổng cộng</span>
                      <span className="text-blue-600">{FerryService.formatCurrency(total)}</span>
                    </div>
                  </div>

                  <ShinyButton
                    onClick={handlePayment}
                    disabled={isProcessing}
                    className="w-full font-medium bg-blue-600 hover:bg-blue-700"
                  >
                    <span className="flex items-center justify-center text-white py-1">
                      {isProcessing
                        ? 'Đang xử lý...'
                        : `Thanh toán ${FerryService.formatCurrency(total)}`}
                    </span>
                  </ShinyButton>

                  <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <Lock className="h-3 w-3" />
                    <span>Thanh toán được bảo mật bằng SSL</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
