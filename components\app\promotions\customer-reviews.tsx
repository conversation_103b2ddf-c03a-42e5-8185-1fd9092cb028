'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Star } from 'lucide-react';
import { useEffect, useRef } from 'react';

interface Review {
  name: string;
  rating: number;
  comment: string;
  avatar: string;
}

interface CustomerReviewsProps {
  reviews?: Review[];
}

const defaultReviews: Review[] = [
  {
    name: '<PERSON>uy<PERSON><PERSON>',
    rating: 5,
    comment: 'Tiết kiệm được 150k cho chuyến đi Đà Nẵng',
    avatar: 'https://i.imgur.com/K7Dgf9k.jpeg',
  },
  {
    name: 'Tr<PERSON><PERSON>h<PERSON>',
    rating: 5,
    comment: 'M<PERSON> giảm giá dễ sử dụng, rất hài lòng',
    avatar: 'https://i.imgur.com/L18dzp5.jpeg',
  },
  {
    name: '<PERSON><PERSON>',
    rating: 4,
    comment: '<PERSON><PERSON><PERSON> vụ tố<PERSON>, sẽ tiếp tục sử dụng',
    avatar: 'https://i.imgur.com/YTKNSeW.jpeg',
  },
  {
    name: '<PERSON><PERSON><PERSON>ng',
    rating: 5,
    comment: 'Giao diện đẹp, đặt vé nhanh chóng',
    avatar: 'https://i.imgur.com/K7Dgf9k.jpeg',
  },
  {
    name: 'Đỗ Minh Tuấn',
    rating: 4,
    comment: 'Hỗ trợ khách hàng nhiệt tình',
    avatar: 'https://i.imgur.com/L18dzp5.jpeg',
  },
];

export default function CustomerReviews({ reviews = defaultReviews }: CustomerReviewsProps) {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    let animationId: number;
    let position = 0;
    const speed = 0.5; // pixels per frame (adjust for speed)
    
    const animate = () => {
      if (!scrollContainer) return;
      
      position += speed;
      const singleSetWidth = (scrollContainer.scrollWidth / 3); // One third because we triplicate
      
      // Reset position when we've moved one full set
      if (position >= singleSetWidth) {
        position = 0;
      }
      
      scrollContainer.style.transform = `translateX(-${position}px)`;
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  // Triple reviews for extra seamless loop
  const triplicatedReviews = [...reviews, ...reviews, ...reviews];

  return (
    <div className="mb-12 mt-12">
      <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-center py-5">
        Khách hàng nói gì về Ocean Pass
      </h2>
      <div className="overflow-hidden">
        <div
          ref={scrollRef}
          className="flex gap-6"
          style={{ 
            willChange: 'transform',
            backfaceVisibility: 'hidden',
            perspective: '1000px'
          }}
        >
          {triplicatedReviews.map((review, index) => (
            <Card key={index} className="flex-shrink-0 w-80">
              <CardContent className="py-6">
                <div className="flex items-center space-x-3 mb-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={review.avatar} alt={review.name} />
                    <AvatarFallback className="text-sm">{review.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-sm">{review.name}</div>
                    <div className="flex">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-600 leading-relaxed">"{review.comment}"</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}