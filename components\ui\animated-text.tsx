'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface AnimatedTextProps {
  words: string[];
  className?: string;
  interval?: number;
}

function AnimatedText({ words, className = '', interval = 2000 }: AnimatedTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const timeoutId = setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, interval);
    return () => clearTimeout(timeoutId);
  }, [currentIndex, words.length, interval, mounted]);

  // Always show first word during SSR and initial render
  if (!mounted) {
    return <span className={className}>{words[0]}</span>;
  }

  return (
    <span
      className={`relative inline-block text-center ${className}`}
      style={{ minHeight: '1.2em', width: 'auto' }}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          className="absolute top-0 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
          initial={{ opacity: 0, y: 20 }}
          transition={
            currentIndex === index
              ? {
                  type: 'spring',
                  stiffness: 200,
                  damping: 20,
                  duration: 0.5,
                }
              : {
                  type: 'spring',
                  stiffness: 300,
                  damping: 30,
                  duration: 0.3,
                }
          }
          animate={
            currentIndex === index
              ? {
                  y: 0,
                  opacity: 1,
                }
              : {
                  y: currentIndex > index ? -20 : 20,
                  opacity: 0,
                }
          }
        >
          {word}
        </motion.span>
      ))}
      {/* Invisible placeholder to maintain proper width */}
      <span className="invisible whitespace-nowrap">
        {words.reduce(
          (longest, current) => (current.length > longest.length ? current : longest),
          words[0]
        )}
      </span>
    </span>
  );
}

export { AnimatedText };
