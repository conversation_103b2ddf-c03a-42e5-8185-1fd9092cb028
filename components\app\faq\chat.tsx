"use client";

import { But<PERSON> } from "@/components/ui/button";

export default function StartChatButton() {
  function handleOpenChat() {
    if (typeof window !== "undefined" && (window as any).$crisp) {
      (window as any).$crisp.push(["do", "chat:show"]);
      (window as any).$crisp.push(["do", "chat:open"]);
    }
  }

  return (
    <Button
      className="w-full bg-purple-600 hover:bg-purple-700"
      onClick={handleOpenChat}
    >
      Bắt đầu chat
    </Button>
  );
}
