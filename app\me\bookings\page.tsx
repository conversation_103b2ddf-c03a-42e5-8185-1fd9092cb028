'use client';

import { Footer } from '@/components/layout/footer';
import Navbar04Page from '@/components/navbar-04/navbar-04';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/lib/auth-context';
import { getBookingsByUserId } from '@/lib/mock-bookings';
import { mockPorts, mockRoutes, mockSchedules, mockVessels } from '@/lib/mock-data';
import { Calendar, CreditCard, Download, Eye, MapPin, Users } from 'lucide-react';
import Link from 'next/link';

export default function BookingsPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Navbar04Page />

        <main className="flex-1">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
            <Alert>
              <AlertDescription>Vui lòng đăng nhập để xem đơn đặt vé.</AlertDescription>
            </Alert>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  const bookings = getBookingsByUserId(user.id);

  const getBookingDetails = (booking: any) => {
    const outboundSchedule = mockSchedules.find((s) => s.id === booking.outboundScheduleId);
    const returnSchedule = booking.returnScheduleId
      ? mockSchedules.find((s) => s.id === booking.returnScheduleId)
      : null;
    const route = mockRoutes.find((r) => r.id === outboundSchedule?.routeId);
    const vessel = mockVessels.find((v) => v.id === outboundSchedule?.vesselId);
    const fromPort = mockPorts.find((p) => p.id === route?.fromPortId);
    const toPort = mockPorts.find((p) => p.id === route?.toPortId);

    return { outboundSchedule, returnSchedule, route, vessel, fromPort, toPort };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Đã xác nhận';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      case 'completed':
        return 'Hoàn thành';
      default:
        return status;
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Navbar04Page />

      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-30">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900">Đơn đặt vé của tôi</h1>
            <p className="text-gray-600 mt-2">Quản lý và xem chi tiết các đơn đặt vé</p>
          </div>

          {bookings.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-gray-500 mb-4">
                  <Calendar className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg">Bạn chưa có đơn đặt vé nào</p>
                  <p className="text-sm">Hãy đặt vé tàu đầu tiên của bạn!</p>
                </div>
                <Link href="/">
                  <Button>Đặt vé ngay</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {bookings.map((booking) => {
                const details = getBookingDetails(booking);

                return (
                  <Card key={booking.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="flex items-center gap-2">
                            Mã đặt vé: {booking.code}
                            <Badge className={getStatusColor(booking.status)}>
                              {getStatusText(booking.status)}
                            </Badge>
                          </CardTitle>
                          <CardDescription>
                            Đặt ngày {new Date(booking.createdAt).toLocaleDateString('vi-VN')}
                          </CardDescription>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-blue-600">
                            {booking.totalAmount.toLocaleString('vi-VN')}đ
                          </p>
                          {booking.discountAmount > 0 && (
                            <p className="text-sm text-green-600">
                              Tiết kiệm {booking.discountAmount.toLocaleString('vi-VN')}đ
                            </p>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Trip Information */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <MapPin className="h-4 w-4 text-gray-500" />
                            <span>
                              {details.fromPort?.name} → {details.toPort?.name}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span>
                              {details.outboundSchedule?.date} -{' '}
                              {details.outboundSchedule?.departureTime}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm">
                            <Users className="h-4 w-4 text-gray-500" />
                            <span>{booking.passengers.length} hành khách</span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <CreditCard className="h-4 w-4 text-gray-500" />
                            <span>
                              {booking.paymentMethod === 'card'
                                ? 'Thẻ tín dụng'
                                : booking.paymentMethod === 'e-wallet'
                                ? 'Ví điện tử'
                                : 'Chuyển khoản'}
                            </span>
                          </div>
                          {booking.tripType === 'round-trip' && (
                            <div className="text-sm text-blue-600">✈️ Vé khứ hồi</div>
                          )}
                        </div>
                      </div>

                      {/* Passengers */}
                      <div>
                        <h4 className="font-medium mb-2">Hành khách:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {booking.passengers.map((passenger, index) => (
                            <div key={passenger.id} className="text-sm bg-gray-50 p-2 rounded">
                              {passenger.name} (
                              {passenger.type === 'adult' ? 'Người lớn' : 'Trẻ em'}) - Ghế{' '}
                              {passenger.seatId}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex justify-center gap-2 pt-4 border-t">
                        <Link href={`/me/bookings/${booking.id}`}>
                          <Button variant="outline" size="sm" className="flex items-center">
                            <Eye className="h-4 w-4 mr-2" />
                            Xem vé
                          </Button>
                        </Link>
                        <Button variant="outline" size="sm" className="flex items-center">
                          <Download className="h-4 w-4 mr-2" />
                          Tải PDF
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
